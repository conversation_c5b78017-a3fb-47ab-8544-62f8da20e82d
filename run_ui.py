#!/usr/bin/env python3
"""
TikTok Bot Web UI Launcher
Khởi chạy giao diện web để quản lý TikTok bot
"""

import os
import sys
import subprocess
import webbrowser
import time
import signal
from threading import Thread

def check_dependencies():
    """Kiểm tra các dependencies cần thiết"""
    print("🔍 Checking dependencies...")
    
    required_packages = ['flask', 'flask_cors']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - OK")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - Missing")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                'flask==3.0.0', 'flask-cors==4.0.0', 'python-dotenv==1.0.0'
            ])
            print("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    
    return True

def check_config():
    """Kiểm tra cấu hình"""
    print("\n🔧 Checking configuration...")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("⚠️ .env file not found, creating from template...")
        if os.path.exists('.env.example'):
            import shutil
            shutil.copy('.env.example', '.env')
            print("✅ .env file created from template")
        else:
            print("❌ .env.example not found")
            return False
    
    # Check bot files
    required_files = ['config.py', 'utils.py', 'scrape.py', 'telegram_logger.py', 'index.py']
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - OK")
        else:
            print(f"❌ {file} - Missing")
            return False
    
    return True

def start_web_ui():
    """Khởi chạy Web UI"""
    print("\n🚀 Starting TikTok Bot Web UI...")
    
    try:
        # Import Flask app
        from app import app
        
        # Start Flask app
        print("📱 Web UI will be available at: http://localhost:5000")
        print("🔧 Dashboard: http://localhost:5000")
        print("⚙️ Settings: http://localhost:5000/settings")
        print("📋 Logs: http://localhost:5000/logs")
        print("🎥 Videos: http://localhost:5000/videos")
        print("\n💡 Press Ctrl+C to stop the Web UI")
        
        # Auto-open browser after a short delay
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open('http://localhost:5000')
                print("🌐 Browser opened automatically")
            except Exception as e:
                print(f"⚠️ Could not open browser automatically: {e}")
        
        browser_thread = Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # Start Flask app
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n⏹️ Web UI stopped by user")
    except Exception as e:
        print(f"\n💥 Failed to start Web UI: {e}")
        return False
    
    return True

def signal_handler(signum, frame):
    """Handle Ctrl+C gracefully"""
    print("\n⏹️ Shutting down Web UI...")
    sys.exit(0)

def main():
    """Main function"""
    print("🎨 TikTok Bot Web UI Launcher")
    print("=" * 50)
    
    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed")
        return 1
    
    # Check configuration
    if not check_config():
        print("\n❌ Configuration check failed")
        return 1
    
    # Start Web UI
    if not start_web_ui():
        print("\n❌ Failed to start Web UI")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
