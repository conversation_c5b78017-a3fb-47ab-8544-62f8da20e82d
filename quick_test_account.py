#!/usr/bin/env python3
# quick_test_account.py

"""
Quick test for TikTok account
"""

import asyncio
from tiktok_account_tester import TikTokAccountTester
from tiktok_account_manager import TikTokAccountManager

async def quick_test():
    """Quick test of first account"""
    print("🧪 Quick TikTok Account Test")
    print("=" * 50)
    
    # Get accounts
    manager = TikTokAccountManager()
    accounts = manager.get_accounts()
    
    if not accounts:
        print("❌ No accounts found!")
        print("💡 Create an account first using the Web UI")
        return
    
    # Find first account (including test accounts for demo)
    test_account = accounts[0]  # Use first account

    if test_account['status'] == 'test':
        print("⚠️ This is a test account (not real TikTok account)")
        print("💡 For real testing, create an account via Web UI")
        print("🧪 Continuing with test account for demo...")

    # if not test_account:
    #     print("❌ No accounts found")
    #     return
    
    print(f"🎯 Testing account: {test_account['username']}")
    print(f"📧 Email: {test_account['email']}")
    print(f"📅 Created: {test_account['created_at']}")
    print(f"📊 Status: {test_account['status']}")
    
    # Test account
    tester = TikTokAccountTester()
    result = await tester.test_account_functionality(test_account)
    
    # Show results
    print("\n📊 Test Results:")
    print("=" * 50)
    
    if result['login']['success']:
        print("✅ LOGIN: SUCCESS")
        if 'profile_info' in result['login']:
            profile = result['login']['profile_info']
            print(f"   Profile URL: {profile.get('profile_url', 'N/A')}")
            print(f"   Display Name: {profile.get('display_username', 'N/A')}")
            print(f"   Followers: {profile.get('followers', 'N/A')}")
    else:
        print("❌ LOGIN: FAILED")
        print(f"   Error: {result['login'].get('error', 'Unknown error')}")
    
    if result['functionality']['success']:
        print("✅ FUNCTIONALITY: PASS")
    else:
        print("❌ FUNCTIONALITY: FAIL")
        print(f"   Error: {result['functionality'].get('error', 'Unknown error')}")
    
    print("\n🎉 Quick test completed!")

if __name__ == "__main__":
    asyncio.run(quick_test())
