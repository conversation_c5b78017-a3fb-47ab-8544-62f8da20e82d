{% extends "base.html" %}

{% block title %}Proxy Manager - TikTok Bot{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-globe me-2"></i>Proxy Management</h5>
            </div>
            <div class="card-body">
                <!-- Add Proxy Form -->
                <div class="mb-4">
                    <h6>Add New Proxy</h6>
                    <div class="row">
                        <div class="col-md-8">
                            <input type="text" id="new-proxy" class="form-control" 
                                   placeholder="http://username:<EMAIL>:8080">
                        </div>
                        <div class="col-md-2">
                            <button id="test-new-proxy" class="btn btn-outline-warning">
                                <i class="fas fa-vial me-1"></i>Test
                            </button>
                        </div>
                        <div class="col-md-2">
                            <button id="add-proxy" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>Add
                            </button>
                        </div>
                    </div>
                    <small class="text-muted">
                        Supported formats: http://proxy:port, **********************:port, socks5://proxy:port
                    </small>
                </div>

                <!-- Current Proxies -->
                <div class="mb-4">
                    <h6>Current Proxies</h6>
                    <div id="proxy-list" class="border rounded p-3">
                        <div class="text-center text-muted">
                            <i class="fas fa-spinner fa-spin me-2"></i>Loading proxies...
                        </div>
                    </div>
                </div>

                <!-- Proxy Test Results -->
                <div id="test-results" class="d-none">
                    <h6>Test Results</h6>
                    <div id="test-output" class="border rounded p-3 bg-light">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Proxy Statistics -->
        <div class="card mb-3">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Proxy Statistics</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 id="total-proxies" class="text-primary">0</h4>
                            <small class="text-muted">Total Proxies</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 id="working-proxies" class="text-success">0</h4>
                        <small class="text-muted">Working</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- TikTok Access Test -->
        <div class="card mb-3">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0"><i class="fas fa-video me-2"></i>TikTok Access Test</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="test-username" class="form-label">Test Username</label>
                    <input type="text" id="test-username" class="form-control" 
                           value="its.sahiba2233" placeholder="TikTok username">
                </div>
                <div class="d-grid gap-2">
                    <button id="test-tiktok-access" class="btn btn-warning">
                        <i class="fas fa-vial me-2"></i>Test TikTok Access
                    </button>
                    <button id="test-all-proxies" class="btn btn-outline-warning">
                        <i class="fas fa-list-check me-2"></i>Test All Proxies
                    </button>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0"><i class="fas fa-tools me-2"></i>Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button id="refresh-proxies" class="btn btn-outline-primary">
                        <i class="fas fa-sync me-2"></i>Refresh List
                    </button>
                    <button id="clear-all-proxies" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-2"></i>Clear All
                    </button>
                    <button id="import-proxies" class="btn btn-outline-info">
                        <i class="fas fa-file-import me-2"></i>Import from File
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Proxies</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="proxy-text" class="form-label">Paste Proxies (one per line)</label>
                    <textarea id="proxy-text" class="form-control" rows="10" 
                              placeholder="http://proxy1:port1&#10;**********************2:port2&#10;socks5://proxy3:port3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="import-confirm" class="btn btn-primary">Import Proxies</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    loadProxyList();
    loadProxyStats();
    
    // Auto-refresh every 30 seconds
    setInterval(loadProxyStats, 30000);
    
    // Event handlers
    $('#add-proxy').click(addProxy);
    $('#test-new-proxy').click(testNewProxy);
    $('#test-tiktok-access').click(testTikTokAccess);
    $('#test-all-proxies').click(testAllProxies);
    $('#refresh-proxies').click(loadProxyList);
    $('#clear-all-proxies').click(clearAllProxies);
    $('#import-proxies').click(function() {
        $('#importModal').modal('show');
    });
    $('#import-confirm').click(importProxies);
    
    // Enter key to add proxy
    $('#new-proxy').keypress(function(e) {
        if (e.which === 13) {
            addProxy();
        }
    });
});

function loadProxyList() {
    $.get('/api/proxy/status', function(data) {
        const container = $('#proxy-list');
        container.empty();
        
        if (data.total_proxies > 0) {
            // This would need to be implemented to return actual proxy list
            container.html(`
                <div class="text-center">
                    <i class="fas fa-globe text-primary me-2"></i>
                    ${data.total_proxies} proxies configured
                    <div class="mt-2">
                        <small class="text-muted">Status: ${data.status}</small>
                    </div>
                </div>
            `);
        } else {
            container.html(`
                <div class="text-center text-muted">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    No proxies configured
                </div>
            `);
        }
    }).fail(function() {
        $('#proxy-list').html(`
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                Failed to load proxy list
            </div>
        `);
    });
}

function loadProxyStats() {
    $.get('/api/proxy/status', function(data) {
        $('#total-proxies').text(data.total_proxies || 0);
        // Working proxies would need to be tracked separately
        $('#working-proxies').text('?');
    });
}

function addProxy() {
    const proxy = $('#new-proxy').val().trim();
    if (!proxy) {
        showAlert('danger', 'Please enter a proxy');
        return;
    }
    
    $.post('/api/proxy/add', {proxy: proxy}, function(data) {
        if (data.success) {
            showAlert('success', data.message);
            $('#new-proxy').val('');
            loadProxyList();
            loadProxyStats();
        } else {
            showAlert('danger', data.message);
        }
    }).fail(function() {
        showAlert('danger', 'Failed to add proxy');
    });
}

function testNewProxy() {
    const proxy = $('#new-proxy').val().trim();
    if (!proxy) {
        showAlert('warning', 'Please enter a proxy to test');
        return;
    }
    
    const button = $('#test-new-proxy');
    const originalText = button.html();
    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Testing...');
    
    $.post('/api/proxy/test', {proxy: proxy}, function(data) {
        if (data.success) {
            showAlert('success', `Proxy working! IP: ${data.ip}`);
        } else {
            showAlert('danger', `Proxy failed: ${data.message}`);
        }
    }).fail(function() {
        showAlert('danger', 'Failed to test proxy');
    }).always(function() {
        button.prop('disabled', false).html(originalText);
    });
}

function testTikTokAccess() {
    const username = $('#test-username').val().trim() || 'its.sahiba2233';
    const button = $('#test-tiktok-access');
    const originalText = button.html();
    
    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Testing...');
    
    $.post('/api/test-tiktok-access', {username: username}, function(data) {
        $('#test-results').removeClass('d-none');
        $('#test-output').html(`
            <div class="mb-2">
                <strong>Test Result:</strong> 
                <span class="badge ${data.success ? 'bg-success' : 'bg-danger'}">
                    ${data.success ? 'Success' : 'Failed'}
                </span>
            </div>
            <div class="mb-2"><strong>Output:</strong></div>
            <pre class="small">${data.output || 'No output'}</pre>
            ${data.error ? `<div class="text-danger"><strong>Error:</strong> ${data.error}</div>` : ''}
        `);
        
        if (data.success) {
            showAlert('success', 'TikTok access test completed');
        } else {
            showAlert('warning', 'TikTok access test had issues');
        }
    }).fail(function() {
        showAlert('danger', 'Failed to run TikTok access test');
    }).always(function() {
        button.prop('disabled', false).html(originalText);
    });
}

function testAllProxies() {
    showAlert('info', 'Testing all proxies... This may take a while');
    // This would need backend implementation
}

function clearAllProxies() {
    if (confirm('Are you sure you want to clear all proxies?')) {
        // This would need backend implementation
        showAlert('info', 'Clear all proxies feature needs to be implemented');
    }
}

function importProxies() {
    const text = $('#proxy-text').val().trim();
    if (!text) {
        showAlert('warning', 'Please paste some proxies');
        return;
    }
    
    const proxies = text.split('\n').filter(line => line.trim());
    showAlert('info', `Importing ${proxies.length} proxies... This feature needs backend implementation`);
    $('#importModal').modal('hide');
}

function showAlert(type, message) {
    const alert = $('<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                   message +
                   '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                   '</div>');
    
    $('.container-fluid').prepend(alert);
    
    setTimeout(function() {
        alert.alert('close');
    }, 5000);
}
</script>
{% endblock %}
