#!/usr/bin/env python3
# test_bot_with_auto_proxy.py

"""
Test TikTok bot with auto proxy functionality
"""

import asyncio
import sys
from proxy_handler import get_auto_proxy
from config import AUTO_PROXY_ENABLED, PREMIUM_PROXY_API_KEY, TARGET_USERNAMES
from telegram_logger import send_telegram_message

async def test_bot_with_auto_proxy():
    """Test bot with auto proxy"""
    print("🤖 Testing TikTok Bot with Auto Proxy")
    print("=" * 50)
    
    # Test 1: Check auto proxy
    print("\n🔍 Step 1: Getting auto proxy...")
    if AUTO_PROXY_ENABLED:
        proxy = get_auto_proxy(PREMIUM_PROXY_API_KEY)
    else:
        proxy = get_auto_proxy()
    
    if proxy:
        print(f"✅ Auto proxy found: {proxy}")
        await send_telegram_message(f"🌐 Auto proxy ready: {proxy}")
    else:
        print("❌ No auto proxy found")
        await send_telegram_message("❌ No auto proxy found")
        return False
    
    # Test 2: Check target usernames
    print(f"\n🎯 Step 2: Target usernames: {TARGET_USERNAMES}")
    if isinstance(TARGET_USERNAMES, list):
        target_list = TARGET_USERNAMES
    else:
        target_list = TARGET_USERNAMES.split(',') if TARGET_USERNAMES else []
    
    if not target_list:
        print("❌ No target usernames configured")
        await send_telegram_message("❌ No target usernames configured")
        return False

    print(f"✅ Found {len(target_list)} target(s): {', '.join(target_list)}")
    await send_telegram_message(f"🎯 Targets: {', '.join(target_list)}")
    
    # Test 3: Test proxy with a simple request
    print(f"\n🧪 Step 3: Testing proxy {proxy}...")
    import requests
    
    try:
        proxies = {
            'http': f'http://{proxy}',
            'https': f'http://{proxy}'
        }
        
        # Try HTTP first, then HTTPS
        try:
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxies,
                timeout=10
            )
        except:
            # If HTTP fails, try HTTPS
            response = requests.get(
                'https://httpbin.org/ip',
                proxies=proxies,
                timeout=10
            )
        
        if response.status_code == 200:
            ip_info = response.json()
            print(f"✅ Proxy working! External IP: {ip_info.get('origin', 'Unknown')}")
            await send_telegram_message(f"✅ Proxy test passed! IP: {ip_info.get('origin', 'Unknown')}")
        else:
            print(f"❌ Proxy test failed with status: {response.status_code}")
            await send_telegram_message(f"❌ Proxy test failed with status: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Proxy test failed: {e}")
        await send_telegram_message(f"❌ Proxy test failed: {e}")
        return False

    print("\n🎉 All tests passed! Bot is ready to run with auto proxy.")
    await send_telegram_message("🎉 TikTok Bot auto proxy test completed successfully!")
    return True

def main():
    """Main function"""
    try:
        result = asyncio.run(test_bot_with_auto_proxy())
        if result:
            print("\n✅ SUCCESS: Bot is ready to run!")
            print("\n💡 Next steps:")
            print("  1. Run: python index.py")
            print("  2. Or use Web UI: python run_ui.py")
        else:
            print("\n❌ FAILED: Please fix the issues above")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
