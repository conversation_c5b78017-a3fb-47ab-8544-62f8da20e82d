#!/usr/bin/env python3
"""
Test Settings Functionality
Test case để kiểm tra việ<PERSON> lưu Target Users và các settings khác
"""

import os
import sys
import json
import time
import requests
import subprocess
from threading import Thread

class SettingsTest:
    def __init__(self):
        self.base_url = "http://127.0.0.1:5001"
        self.server_process = None
        
    def start_test_server(self):
        """Start test server"""
        print("🚀 Starting test server...")
        
        try:
            # Start Flask app in test mode
            self.server_process = subprocess.Popen(
                [sys.executable, '-c', '''
import sys
sys.path.append(".")
from app import app
app.run(debug=False, host="127.0.0.1", port=5001, use_reloader=False)
                '''],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            # Wait for server to start
            time.sleep(3)
            
            # Test if server is running
            try:
                response = requests.get(f"{self.base_url}/", timeout=5)
                if response.status_code == 200:
                    print("✅ Test server started successfully")
                    return True
            except:
                pass
                
            print("❌ Failed to start test server")
            return False
            
        except Exception as e:
            print(f"❌ Error starting test server: {e}")
            return False
    
    def stop_test_server(self):
        """Stop test server"""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
            print("⏹️ Test server stopped")
    
    def backup_env_file(self):
        """Backup current .env file"""
        if os.path.exists('.env'):
            import shutil
            shutil.copy('.env', '.env.backup')
            print("💾 Backed up .env file")
    
    def restore_env_file(self):
        """Restore .env file from backup"""
        if os.path.exists('.env.backup'):
            import shutil
            shutil.copy('.env.backup', '.env')
            os.remove('.env.backup')
            print("🔄 Restored .env file")
    
    def read_env_file(self):
        """Read current .env file"""
        env_vars = {}
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key] = value
        return env_vars
    
    def test_target_users_api(self):
        """Test Target Users API"""
        print("\n🧪 Testing Target Users API...")
        
        # Test data
        test_usernames = ["testuser1", "testuser2", "testuser3"]
        
        # Send POST request
        try:
            response = requests.post(
                f"{self.base_url}/api/settings/target-users",
                json={"target_usernames": test_usernames},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print(f"✅ API Response: {data['message']}")
                    
                    # Verify .env file was updated
                    env_vars = self.read_env_file()
                    target_usernames = env_vars.get('TARGET_USERNAMES', '')
                    
                    if target_usernames == ','.join(test_usernames):
                        print("✅ .env file updated correctly")
                        print(f"   TARGET_USERNAMES = {target_usernames}")
                        return True
                    else:
                        print(f"❌ .env file not updated correctly")
                        print(f"   Expected: {','.join(test_usernames)}")
                        print(f"   Got: {target_usernames}")
                        return False
                else:
                    print(f"❌ API returned error: {data.get('message')}")
                    return False
            else:
                print(f"❌ API request failed with status {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Exception during API test: {e}")
            return False
    
    def test_tiktok_settings_api(self):
        """Test TikTok Settings API"""
        print("\n🧪 Testing TikTok Settings API...")
        
        test_data = {
            "tiktok_username": "test_username",
            "tiktok_password": "test_password"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/settings/tiktok",
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print(f"✅ TikTok API: {data['message']}")
                    
                    # Verify .env file
                    env_vars = self.read_env_file()
                    if (env_vars.get('TIKTOK_USERNAME') == test_data['tiktok_username'] and
                        env_vars.get('TIKTOK_PASSWORD') == test_data['tiktok_password']):
                        print("✅ TikTok settings saved correctly")
                        return True
                    else:
                        print("❌ TikTok settings not saved correctly")
                        return False
                else:
                    print(f"❌ TikTok API error: {data.get('message')}")
                    return False
            else:
                print(f"❌ TikTok API failed with status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Exception during TikTok API test: {e}")
            return False
    
    def test_telegram_settings_api(self):
        """Test Telegram Settings API"""
        print("\n🧪 Testing Telegram Settings API...")
        
        test_data = {
            "telegram_token": "123456789:ABCdefGHIjklMNOpqrsTUVwxyz",
            "telegram_user_id": 987654321
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/settings/telegram",
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print(f"✅ Telegram API: {data['message']}")
                    
                    # Verify .env file
                    env_vars = self.read_env_file()
                    if (env_vars.get('TELEGRAM_BOT_TOKEN') == test_data['telegram_token'] and
                        env_vars.get('TELEGRAM_USER_ID') == str(test_data['telegram_user_id'])):
                        print("✅ Telegram settings saved correctly")
                        return True
                    else:
                        print("❌ Telegram settings not saved correctly")
                        return False
                else:
                    print(f"❌ Telegram API error: {data.get('message')}")
                    return False
            else:
                print(f"❌ Telegram API failed with status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Exception during Telegram API test: {e}")
            return False
    
    def test_settings_page_load(self):
        """Test Settings page loads correctly"""
        print("\n🧪 Testing Settings page load...")
        
        try:
            response = requests.get(f"{self.base_url}/settings", timeout=10)
            
            if response.status_code == 200:
                content = response.text
                
                # Check if page contains expected elements
                checks = [
                    "Target Users" in content,
                    "TikTok Account" in content,
                    "Telegram Settings" in content,
                    "target-usernames" in content,
                    "tiktok-username" in content,
                    "telegram-token" in content
                ]
                
                if all(checks):
                    print("✅ Settings page loaded correctly")
                    return True
                else:
                    print("❌ Settings page missing expected elements")
                    return False
            else:
                print(f"❌ Settings page failed to load: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Exception loading settings page: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("🧪 Settings Functionality Test Suite")
        print("=" * 50)
        
        # Backup current settings
        self.backup_env_file()
        
        try:
            # Start test server
            if not self.start_test_server():
                return False
            
            # Run tests
            tests = [
                ("Settings Page Load", self.test_settings_page_load),
                ("Target Users API", self.test_target_users_api),
                ("TikTok Settings API", self.test_tiktok_settings_api),
                ("Telegram Settings API", self.test_telegram_settings_api)
            ]
            
            results = []
            for test_name, test_func in tests:
                print(f"\n{'='*20} {test_name} {'='*20}")
                try:
                    result = test_func()
                    results.append((test_name, result))
                except Exception as e:
                    print(f"❌ {test_name} crashed: {e}")
                    results.append((test_name, False))
            
            # Summary
            print("\n" + "="*50)
            print("📊 TEST RESULTS")
            print("="*50)
            
            passed = 0
            for test_name, result in results:
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"{test_name:25} {status}")
                if result:
                    passed += 1
            
            print(f"\nResult: {passed}/{len(results)} tests passed")
            
            if passed == len(results):
                print("\n🎉 All settings tests passed!")
                print("💡 Target Users saving should work correctly now")
            else:
                print("\n⚠️ Some tests failed. Check the issues above.")
            
            return passed == len(results)
            
        finally:
            # Cleanup
            self.stop_test_server()
            self.restore_env_file()

def main():
    """Main function"""
    test = SettingsTest()
    
    try:
        success = test.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
        test.stop_test_server()
        test.restore_env_file()
        return 1
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        test.stop_test_server()
        test.restore_env_file()
        return 1

if __name__ == "__main__":
    sys.exit(main())
