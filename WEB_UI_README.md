# 🎨 TikTok Bot Web UI

Giao diện web hiện đại để quản lý TikTok Bot một cách dễ dàng và trực quan.

## ✨ Tính năng chính

### 📊 **Dashboard**
- Xem trạng thái bot real-time
- Thống kê tổng quan (videos, shares, users)
- <PERSON><PERSON>ều khiển bot (Start/Stop)
- Hoạt động gần đây

### ⚙️ **Settings**
- **Target Users**: Quản lý danh sách TikTok users cần theo dõi
- **TikTok Account**: C<PERSON>u hình tài khoản TikTok
- **Telegram**: Thiết lập thông báo Telegram
- **Share Rules**: Tùy chỉnh quy tắc share dựa trên số like
- **Bot Settings**: Cấu hình thời gian check

### 📋 **Logs**
- Xem logs real-time
- Filter theo level (Info, Warning, Error)
- T<PERSON><PERSON> kiếm logs
- Export logs
- Auto-refresh

### 🎥 **Videos**
- Lịch sử tất cả videos đã xử lý
- Thống kê shares
- Filter và search
- Export dữ liệu
- Pagination

## 🚀 Cách sử dụng

### **Cách 1: Chạy trực tiếp**
```bash
python run_ui.py
```

### **Cách 2: Chạy Flask app**
```bash
python app.py
```

### **Cách 3: Cài đặt dependencies trước**
```bash
pip install flask flask-cors python-dotenv
python app.py
```

## 🌐 Truy cập Web UI

Sau khi khởi chạy, truy cập:
- **Dashboard**: http://localhost:5000
- **Settings**: http://localhost:5000/settings  
- **Logs**: http://localhost:5000/logs
- **Videos**: http://localhost:5000/videos

## 📱 Giao diện

### **Dashboard**
![Dashboard Preview]
- Trạng thái bot với indicator màu
- Nút Start/Stop bot
- Thống kê tổng quan
- Danh sách target users
- Hoạt động gần đây

### **Settings Page**
![Settings Preview]
- **Target Users**: Thêm/xóa TikTok usernames
- **TikTok Account**: Username và password
- **Telegram**: Bot token và User ID với test connection
- **Share Rules**: Cấu hình số shares theo likes
- **Actions**: Export/Import/Reset settings

### **Logs Page**
![Logs Preview]
- Terminal-style logs với màu sắc
- Filter theo level
- Search functionality
- Auto-scroll option
- Download logs

### **Videos Page**
![Videos Preview]
- Bảng danh sách videos
- Thống kê shares
- Filter và search
- Pagination
- Export CSV

## 🔧 Tính năng nâng cao

### **Real-time Updates**
- Bot status cập nhật mỗi 5 giây
- Logs refresh tự động
- Notifications khi có thay đổi

### **Responsive Design**
- Tương thích mobile
- Bootstrap 5 UI
- Dark mode support
- Font Awesome icons

### **API Endpoints**
```
GET  /api/bot/status     - Trạng thái bot
POST /api/bot/start      - Start bot
POST /api/bot/stop       - Stop bot
GET  /api/logs           - Lấy logs
GET  /api/stats          - Thống kê
GET  /api/settings       - Cấu hình hiện tại
POST /api/settings       - Lưu cấu hình
POST /api/test-telegram  - Test Telegram
```

## 🛠️ Cấu trúc files

```
├── app.py                 # Flask application
├── run_ui.py             # UI launcher script
├── templates/            # HTML templates
│   ├── base.html         # Base template
│   ├── dashboard.html    # Dashboard page
│   ├── settings.html     # Settings page
│   ├── logs.html         # Logs page
│   └── videos.html       # Videos page
├── static/               # Static files
│   ├── css/
│   │   └── style.css     # Custom styles
│   └── js/
│       └── app.js        # JavaScript functions
└── WEB_UI_README.md      # This file
```

## 🎯 Workflow sử dụng

### **Lần đầu setup:**
1. Chạy `python run_ui.py`
2. Truy cập Settings
3. Cấu hình Target Users
4. Cấu hình Telegram (optional)
5. Test Telegram connection
6. Về Dashboard và Start bot

### **Sử dụng hàng ngày:**
1. Mở Web UI
2. Check Dashboard để xem trạng thái
3. Xem Logs nếu có vấn đề
4. Thêm/bớt Target Users khi cần
5. Xem Videos history

## 🔒 Bảo mật

- Không expose ra internet (chỉ localhost)
- Credentials được lưu trong .env
- Session-based authentication (có thể thêm)
- CORS protection

## 🐛 Troubleshooting

### **Web UI không khởi chạy được:**
```bash
pip install flask flask-cors python-dotenv
python run_ui.py
```

### **Bot không start được:**
- Kiểm tra dependencies: `pip install -r requirements.txt`
- Kiểm tra Playwright: `playwright install chromium`
- Xem logs để debug

### **Telegram không hoạt động:**
- Kiểm tra Bot Token và User ID
- Test connection trong Settings
- Đảm bảo đã start bot với /start

### **Không thấy videos:**
- Bot cần chạy một thời gian để có dữ liệu
- Kiểm tra Target Users đã cấu hình chưa
- Xem logs để kiểm tra lỗi

## 🎨 Customization

### **Thay đổi theme:**
Chỉnh sửa `static/css/style.css`:
```css
:root {
    --primary-color: #your-color;
    --tiktok-color: #your-tiktok-color;
}
```

### **Thêm tính năng:**
1. Thêm route trong `app.py`
2. Tạo template trong `templates/`
3. Thêm JavaScript trong `static/js/app.js`

## 📞 Support

Nếu gặp vấn đề:
1. Kiểm tra logs trong Web UI
2. Chạy `python test_bot.py` để test
3. Xem console output khi chạy `python run_ui.py`

## 🎉 Kết luận

Web UI giúp bạn:
- ✅ Quản lý bot dễ dàng hơn
- ✅ Theo dõi hoạt động real-time  
- ✅ Cấu hình nhanh chóng
- ✅ Debug hiệu quả
- ✅ Trải nghiệm user-friendly

**Enjoy your TikTok Bot with beautiful Web UI! 🚀**
