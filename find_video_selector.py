#!/usr/bin/env python3
# find_video_selector.py

"""
Find TikTok video selector on user page
"""

import asyncio
from playwright.async_api import async_playwright

async def find_video_selector():
    """Find video selector on TikTok user page"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # Show browser
        context = await browser.new_context()
        page = await context.new_page()
        
        # Go to user page
        user_url = "https://www.tiktok.com/@hiamnoone"
        print(f"👤 Loading user page: {user_url}")
        
        await page.goto(user_url, wait_until='load')
        await page.wait_for_timeout(5000)  # Wait for page to load
        
        # Try different video selectors
        selectors = [
            "div[data-e2e='user-post-item'] a",
            "div[data-e2e='user-post-item']",
            "[data-e2e='user-post-item']",
            "[data-e2e*='post']",
            "[data-e2e*='video']",
            "a[href*='/video/']",
            "div[class*='video'] a",
            "div[class*='post'] a",
            ".video-feed-item a",
            "[data-testid*='video']",
            "[data-testid*='post']"
        ]
        
        print("\n🔍 Testing video selectors:")
        print("-" * 50)
        
        for i, selector in enumerate(selectors):
            try:
                elements = await page.query_selector_all(selector)
                if elements:
                    print(f"✅ {i+1}. {selector} -> Found {len(elements)} element(s)")
                    
                    # Get href of first element if it's a link
                    try:
                        if elements[0].tag_name == 'a' or await elements[0].get_attribute('href'):
                            href = await elements[0].get_attribute('href')
                            print(f"   First href: {href}")
                    except:
                        pass
                        
                    # Get some attributes
                    try:
                        attrs = await elements[0].evaluate("el => Array.from(el.attributes).map(attr => `${attr.name}='${attr.value}'`).slice(0,3).join(' ')")
                        print(f"   Attributes: {attrs}...")
                    except:
                        pass
                        
                else:
                    print(f"❌ {i+1}. {selector} -> Not found")
            except Exception as e:
                print(f"❌ {i+1}. {selector} -> Error: {e}")
        
        print("\n🔍 Looking for all links with '/video/' in href:")
        print("-" * 50)
        
        try:
            all_links = await page.query_selector_all("a")
            video_links = []
            
            for link in all_links:
                try:
                    href = await link.get_attribute('href')
                    if href and '/video/' in href:
                        video_links.append(href)
                except:
                    pass
            
            print(f"Found {len(video_links)} video links:")
            for i, link in enumerate(video_links[:10]):  # Show first 10
                print(f"  {i+1}. {link}")
                
        except Exception as e:
            print(f"Error getting video links: {e}")
        
        print("\n🔍 Looking for elements with data-e2e attributes:")
        print("-" * 50)
        
        try:
            data_e2e_elements = await page.query_selector_all("[data-e2e]")
            print(f"Found {len(data_e2e_elements)} elements with data-e2e")
            
            unique_data_e2e = set()
            for element in data_e2e_elements:
                try:
                    data_e2e = await element.get_attribute('data-e2e')
                    if data_e2e:
                        unique_data_e2e.add(data_e2e)
                except:
                    pass
            
            print("Unique data-e2e values:")
            for value in sorted(unique_data_e2e):
                if 'post' in value.lower() or 'video' in value.lower():
                    print(f"  ⭐ {value}")
                else:
                    print(f"     {value}")
                    
        except Exception as e:
            print(f"Error getting data-e2e elements: {e}")
        
        print("\n⏸️ Browser will stay open for 30 seconds for manual inspection...")
        await page.wait_for_timeout(30000)
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(find_video_selector())
