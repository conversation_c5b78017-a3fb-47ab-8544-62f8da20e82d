# telegram_logger.py

import asyncio
import aiohttp
import os
from config import TELEGRAM_BOT_TOKEN, TELEGRAM_USER_ID

async def send_telegram_message(message: str):
    """Send message to Telegram asynchronously"""
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_USER_ID:
        print("[WARNING] Telegram credentials not configured")
        return

    try:
        # Clean message to avoid encoding issues
        clean_message = message.encode('utf-8', errors='ignore').decode('utf-8')

        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
        payload = {
            "chat_id": TELEGRAM_USER_ID,
            "text": clean_message[:4096],  # Telegram message limit
            "parse_mode": "HTML"
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, data=payload, timeout=10) as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"[ERROR] Telegram API error: {response.status} - {error_text}")
                else:
                    print(f"[INFO] Telegram message sent successfully")

    except asyncio.TimeoutError:
        print("[ERROR] Telegram message timeout")
    except Exception as e:
        print(f"[ERROR] Failed to send Telegram message: {e}")

async def send_telegram_photo(photo_path: str, caption: str = ""):
    """Send photo to Telegram asynchronously"""
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_USER_ID:
        print("[WARNING] Telegram credentials not configured")
        return

    if not os.path.exists(photo_path):
        print(f"[ERROR] Photo file not found: {photo_path}")
        return

    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendPhoto"

        async with aiohttp.ClientSession() as session:
            with open(photo_path, "rb") as photo_file:
                data = aiohttp.FormData()
                data.add_field('chat_id', str(TELEGRAM_USER_ID))
                data.add_field('caption', caption[:1024])  # Caption limit
                data.add_field('photo', photo_file, filename=os.path.basename(photo_path))

                async with session.post(url, data=data, timeout=30) as response:
                    if response.status != 200:
                        print(f"[ERROR] Telegram photo API error: {response.status}")

    except asyncio.TimeoutError:
        print("[ERROR] Telegram photo timeout")
    except Exception as e:
        print(f"[ERROR] Failed to send Telegram photo: {e}")

# Synchronous versions for backward compatibility
def send_telegram_message_sync(message: str):
    """Synchronous wrapper for send_telegram_message"""
    try:
        asyncio.run(send_telegram_message(message))
    except Exception as e:
        print(f"[ERROR] Failed to send sync Telegram message: {e}")

def send_telegram_photo_sync(photo_path: str, caption: str = ""):
    """Synchronous wrapper for send_telegram_photo"""
    try:
        asyncio.run(send_telegram_photo(photo_path, caption))
    except Exception as e:
        print(f"[ERROR] Failed to send sync Telegram photo: {e}")