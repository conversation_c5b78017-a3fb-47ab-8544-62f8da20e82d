#!/usr/bin/env python3
"""
Test script for TikTok Bot Web UI
Ki<PERSON>m tra các thành phần của Web UI
"""

import os
import sys
import requests
import time
import subprocess
from threading import Thread

def test_flask_import():
    """Test Flask imports"""
    print("🔍 Testing Flask imports...")
    
    try:
        import flask
        print("✅ Flask - OK")
    except ImportError:
        print("❌ Flask - Missing")
        return False
    
    try:
        import flask_cors
        print("✅ Flask-CORS - OK")
    except ImportError:
        print("❌ Flask-CORS - Missing")
        return False
    
    return True

def test_templates():
    """Test template files"""
    print("\n🔍 Testing template files...")
    
    templates = [
        'templates/base.html',
        'templates/dashboard.html', 
        'templates/settings.html',
        'templates/logs.html',
        'templates/videos.html'
    ]
    
    for template in templates:
        if os.path.exists(template):
            print(f"✅ {template} - OK")
        else:
            print(f"❌ {template} - Missing")
            return False
    
    return True

def test_static_files():
    """Test static files"""
    print("\n🔍 Testing static files...")
    
    static_files = [
        'static/css/style.css',
        'static/js/app.js'
    ]
    
    for static_file in static_files:
        if os.path.exists(static_file):
            print(f"✅ {static_file} - OK")
        else:
            print(f"❌ {static_file} - Missing")
            return False
    
    return True

def test_app_import():
    """Test app.py import"""
    print("\n🔍 Testing app.py import...")
    
    try:
        from app import app
        print("✅ app.py import - OK")
        return True
    except Exception as e:
        print(f"❌ app.py import - ERROR: {e}")
        return False

def start_test_server():
    """Start test server"""
    print("\n🚀 Starting test server...")
    
    try:
        from app import app
        
        # Start server in a separate thread
        def run_server():
            app.run(debug=False, host='127.0.0.1', port=5001, use_reloader=False)
        
        server_thread = Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Wait for server to start
        time.sleep(3)
        
        return True
    except Exception as e:
        print(f"❌ Failed to start test server: {e}")
        return False

def test_endpoints():
    """Test API endpoints"""
    print("\n🔍 Testing API endpoints...")
    
    base_url = "http://127.0.0.1:5001"
    
    endpoints = [
        ('/', 'Dashboard'),
        ('/settings', 'Settings'),
        ('/logs', 'Logs'),
        ('/videos', 'Videos'),
        ('/api/bot/status', 'Bot Status API'),
        ('/api/logs', 'Logs API'),
        ('/api/stats', 'Stats API')
    ]
    
    success_count = 0
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} ({endpoint}) - OK")
                success_count += 1
            else:
                print(f"⚠️ {name} ({endpoint}) - Status {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {name} ({endpoint}) - ERROR: {e}")
    
    print(f"\n📊 Endpoints test result: {success_count}/{len(endpoints)} passed")
    return success_count == len(endpoints)

def test_bot_integration():
    """Test bot integration"""
    print("\n🔍 Testing bot integration...")
    
    required_files = [
        'config.py',
        'utils.py', 
        'scrape.py',
        'telegram_logger.py',
        'index.py'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - OK")
        else:
            print(f"❌ {file} - Missing")
            return False
    
    # Test imports
    try:
        import config
        import utils
        import telegram_logger
        print("✅ Bot modules import - OK")
    except Exception as e:
        print(f"❌ Bot modules import - ERROR: {e}")
        return False
    
    return True

def main():
    """Main test function"""
    print("🧪 TikTok Bot Web UI Test Suite")
    print("=" * 50)
    
    tests = [
        ("Flask Import", test_flask_import),
        ("Template Files", test_templates),
        ("Static Files", test_static_files),
        ("App Import", test_app_import),
        ("Bot Integration", test_bot_integration)
    ]
    
    # Run basic tests
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed: {e}")
            results.append((test_name, False))
    
    # Start server and test endpoints
    print(f"\n{'='*20} Server Test {'='*20}")
    if start_test_server():
        endpoint_result = test_endpoints()
        results.append(("Endpoints", endpoint_result))
    else:
        results.append(("Endpoints", False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nResult: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Web UI is ready to use.")
        print("💡 Run: python run_ui.py")
        print("🌐 Or: python app.py")
    else:
        print("\n⚠️ Some tests failed. Please fix issues before using Web UI.")
        
        # Provide specific guidance
        if not any(r[1] for r in results if r[0] == "Flask Import"):
            print("\n📦 Install Flask: pip install flask flask-cors python-dotenv")
        
        if not any(r[1] for r in results if r[0] in ["Template Files", "Static Files"]):
            print("\n📁 Make sure all template and static files are present")
        
        if not any(r[1] for r in results if r[0] == "Bot Integration"):
            print("\n🤖 Make sure bot files are present and working")
    
    return 0 if passed == len(results) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        sys.exit(1)
