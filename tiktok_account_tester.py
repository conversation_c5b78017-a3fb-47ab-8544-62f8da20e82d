#!/usr/bin/env python3
# tiktok_account_tester.py

"""
Test TikTok accounts to verify they work
"""

import asyncio
import json
from datetime import datetime
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
from telegram_logger import send_telegram_message
from utils import log_to_console
from tiktok_account_manager import TikTokAccountManager

class TikTokAccountTester:
    def __init__(self):
        self.account_manager = TikTokAccountManager()
    
    async def test_account_login(self, account_data, proxy=None):
        """Test if account can login to TikTok"""
        username = account_data['username']
        email = account_data['email']
        password = account_data['password']
        
        log_to_console(f"🧪 Testing account login: {username}")
        await send_telegram_message(f"🧪 Testing login for: {username}")
        
        try:
            async with async_playwright() as p:
                # Browser setup
                browser_args = [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled'
                ]
                
                if proxy:
                    browser_args.append(f'--proxy-server=http://{proxy}')
                
                browser = await p.chromium.launch(
                    headless=False,  # Show browser for debugging
                    args=browser_args
                )
                
                context = await browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                )
                
                page = await context.new_page()
                
                # Navigate to TikTok login
                await page.goto('https://www.tiktok.com/login', wait_until='load')
                await asyncio.sleep(3)
                
                # Try to find login with email option
                login_selectors = [
                    'div:has-text("Use email")',
                    'button:has-text("Use email")',
                    'a:has-text("Use email")',
                    '[data-e2e="email-login"]'
                ]
                
                email_login_clicked = False
                for selector in login_selectors:
                    try:
                        email_btn = await page.query_selector(selector)
                        if email_btn:
                            await email_btn.click()
                            email_login_clicked = True
                            break
                    except:
                        continue
                
                if not email_login_clicked:
                    log_to_console("Could not find email login option")
                    await browser.close()
                    return {"success": False, "error": "Email login option not found"}
                
                await asyncio.sleep(2)
                
                # Fill email
                email_filled = False
                email_selectors = [
                    'input[type="email"]',
                    'input[name="email"]',
                    'input[placeholder*="email"]',
                    'input[placeholder*="Email"]'
                ]
                
                for selector in email_selectors:
                    try:
                        email_field = await page.query_selector(selector)
                        if email_field:
                            await email_field.fill(email)
                            email_filled = True
                            break
                    except:
                        continue
                
                if not email_filled:
                    log_to_console("Could not find email field")
                    await browser.close()
                    return {"success": False, "error": "Email field not found"}
                
                # Fill password
                password_filled = False
                password_selectors = [
                    'input[type="password"]',
                    'input[name="password"]',
                    'input[placeholder*="password"]',
                    'input[placeholder*="Password"]'
                ]
                
                for selector in password_selectors:
                    try:
                        password_field = await page.query_selector(selector)
                        if password_field:
                            await password_field.fill(password)
                            password_filled = True
                            break
                    except:
                        continue
                
                if not password_filled:
                    log_to_console("Could not find password field")
                    await browser.close()
                    return {"success": False, "error": "Password field not found"}
                
                log_to_console("✅ Login form filled")
                
                # Try to click login button
                login_button_selectors = [
                    'button[type="submit"]',
                    'button:has-text("Log in")',
                    'button:has-text("Login")',
                    '[data-e2e="login-button"]'
                ]
                
                login_clicked = False
                for selector in login_button_selectors:
                    try:
                        login_btn = await page.query_selector(selector)
                        if login_btn:
                            await login_btn.click()
                            login_clicked = True
                            break
                    except:
                        continue
                
                if not login_clicked:
                    log_to_console("⚠️ Could not find login button - manual intervention needed")
                    await send_telegram_message(f"⚠️ {username}: Manual login needed")
                    
                    print("\n" + "="*60)
                    print("🤖 MANUAL LOGIN NEEDED")
                    print("="*60)
                    print(f"Account: {username}")
                    print(f"Email: {email}")
                    print(f"Password: {password}")
                    print("1. Click 'Log in' button manually")
                    print("2. Solve CAPTCHA if needed")
                    print("3. Press Enter when logged in...")
                    print("="*60)
                    
                    input("Press Enter when login is complete...")
                
                # Wait for login to complete
                await asyncio.sleep(5)
                
                # Check if login was successful
                current_url = page.url
                page_title = await page.title()
                
                # Check for login success indicators
                success_indicators = [
                    'tiktok.com' in current_url and 'login' not in current_url,
                    'For You' in page_title,
                    'TikTok' in page_title and 'Login' not in page_title
                ]
                
                if any(success_indicators):
                    log_to_console(f"✅ Login successful for {username}")
                    await send_telegram_message(f"✅ {username}: Login successful!")
                    
                    # Try to get profile info
                    profile_info = await self.get_profile_info(page)
                    
                    await browser.close()
                    return {
                        "success": True,
                        "profile_info": profile_info,
                        "login_url": current_url
                    }
                else:
                    log_to_console(f"❌ Login failed for {username}")
                    await send_telegram_message(f"❌ {username}: Login failed")
                    
                    # Take screenshot for debugging
                    screenshot_path = f"login_failed_{username}.png"
                    await page.screenshot(path=screenshot_path)
                    
                    await browser.close()
                    return {
                        "success": False,
                        "error": "Login failed",
                        "current_url": current_url,
                        "page_title": page_title
                    }
                
        except Exception as e:
            log_to_console(f"❌ Error testing {username}: {e}")
            await send_telegram_message(f"❌ {username}: Test error - {str(e)[:100]}")
            return {"success": False, "error": str(e)}
    
    async def get_profile_info(self, page):
        """Get profile information from logged in account"""
        try:
            # Try to navigate to profile
            profile_selectors = [
                'a[href*="/profile"]',
                'button:has-text("Profile")',
                '[data-e2e="profile-icon"]'
            ]
            
            for selector in profile_selectors:
                try:
                    profile_btn = await page.query_selector(selector)
                    if profile_btn:
                        await profile_btn.click()
                        break
                except:
                    continue
            
            await asyncio.sleep(3)
            
            # Get profile information
            profile_info = {}
            
            # Try to get username
            username_selectors = [
                'h1[data-e2e="user-title"]',
                'h1[data-e2e="profile-title"]',
                '.username'
            ]
            
            for selector in username_selectors:
                try:
                    username_element = await page.query_selector(selector)
                    if username_element:
                        profile_info['display_username'] = await username_element.inner_text()
                        break
                except:
                    continue
            
            # Try to get follower count
            follower_selectors = [
                '[data-e2e="followers-count"]',
                'strong:has-text("Followers")',
                '.follower-count'
            ]
            
            for selector in follower_selectors:
                try:
                    follower_element = await page.query_selector(selector)
                    if follower_element:
                        profile_info['followers'] = await follower_element.inner_text()
                        break
                except:
                    continue
            
            profile_info['profile_url'] = page.url
            return profile_info
            
        except Exception as e:
            log_to_console(f"Error getting profile info: {e}")
            return {"error": str(e)}
    
    async def test_account_functionality(self, account_data, proxy=None):
        """Test account functionality (like, follow, etc.)"""
        username = account_data['username']
        
        log_to_console(f"🔧 Testing account functionality: {username}")
        await send_telegram_message(f"🔧 Testing functionality for: {username}")
        
        # First test login
        login_result = await self.test_account_login(account_data, proxy)
        
        if not login_result['success']:
            return {
                "login": login_result,
                "functionality": {"success": False, "error": "Login failed"}
            }
        
        # If login successful, test basic functionality
        # (This would be expanded with actual functionality tests)
        
        return {
            "login": login_result,
            "functionality": {"success": True, "message": "Basic functionality test passed"}
        }
    
    async def test_all_accounts(self):
        """Test all accounts in the system"""
        log_to_console("🧪 Testing all TikTok accounts...")
        await send_telegram_message("🧪 Starting account testing...")
        
        accounts = self.account_manager.get_accounts()
        
        if not accounts:
            log_to_console("❌ No accounts found to test")
            await send_telegram_message("❌ No accounts found to test")
            return
        
        results = []
        
        for i, account in enumerate(accounts):
            if account['status'] == 'test':
                log_to_console(f"⏭️ Skipping test account: {account['username']}")
                continue
            
            log_to_console(f"🧪 Testing account {i+1}/{len(accounts)}: {account['username']}")
            
            # Test account
            result = await self.test_account_functionality(account)
            result['username'] = account['username']
            result['test_time'] = datetime.now().isoformat()
            
            results.append(result)
            
            # Update account status based on test result
            if result['login']['success']:
                self.account_manager.update_account_status(
                    account['username'], 
                    'verified', 
                    f"Login test passed at {datetime.now().strftime('%Y-%m-%d %H:%M')}"
                )
            else:
                self.account_manager.update_account_status(
                    account['username'], 
                    'login_failed', 
                    f"Login test failed: {result['login'].get('error', 'Unknown error')}"
                )
            
            # Wait between tests
            if i < len(accounts) - 1:
                wait_time = 30  # 30 seconds between tests
                log_to_console(f"⏳ Waiting {wait_time} seconds before next test...")
                await asyncio.sleep(wait_time)
        
        # Summary
        successful_logins = sum(1 for r in results if r['login']['success'])
        total_tested = len(results)
        
        log_to_console(f"✅ Account testing completed: {successful_logins}/{total_tested} successful")
        await send_telegram_message(f"✅ Testing completed: {successful_logins}/{total_tested} accounts working")
        
        return results

# Example usage
async def main():
    """Test accounts"""
    tester = TikTokAccountTester()
    
    # Test all accounts
    results = await tester.test_all_accounts()
    
    # Print results
    print("\n📊 Test Results Summary:")
    print("=" * 50)
    for result in results:
        status = "✅ PASS" if result['login']['success'] else "❌ FAIL"
        print(f"{status} {result['username']}")
        if not result['login']['success']:
            print(f"   Error: {result['login'].get('error', 'Unknown')}")

if __name__ == "__main__":
    asyncio.run(main())
