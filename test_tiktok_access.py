#!/usr/bin/env python3
"""
Test TikTok Access
Script để test việ<PERSON> truy cập TikTok và xử lý login popup
"""

import asyncio
import sys
from playwright.async_api import async_playwright

async def test_tiktok_access():
    """Test TikTok access with different approaches"""
    print("🧪 Testing TikTok Access")
    print("=" * 40)
    
    test_users = ["its.sahiba2233", "iamvirk", "charlidamelio"]
    
    async with async_playwright() as p:
        # Test 1: Headless mode
        print("\n🔍 Test 1: Headless Mode")
        await test_with_mode(p, headless=True, test_users=test_users[:1])
        
        # Test 2: Visible mode (để xem CAPTCHA)
        print("\n🔍 Test 2: Visible Mode (to see CAPTCHA)")
        await test_with_mode(p, headless=False, test_users=test_users[:1])
        
        # Test 3: Different user agents
        print("\n🔍 Test 3: Different User Agents")
        await test_user_agents(p, test_users[0])

async def test_with_mode(p, headless, test_users):
    """Test with specific browser mode"""
    try:
        browser = await p.chromium.launch(
            headless=headless,
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security'
            ]
        )
        
        page = await browser.new_page()
        
        for username in test_users:
            url = f"https://www.tiktok.com/@{username}"
            print(f"\n📱 Testing @{username}")
            
            try:
                # Navigate to page
                print(f"🌐 Navigating to {url}")
                await page.goto(url, timeout=30000)
                
                # Wait and check what we get
                await page.wait_for_timeout(5000)
                
                # Check page title
                title = await page.title()
                print(f"📄 Page title: {title}")
                
                # Check for login popup
                login_popup = await check_login_popup(page)
                
                # Check for videos
                videos_found = await check_videos(page)
                
                # Check for CAPTCHA
                captcha_found = await check_captcha(page)
                
                # Summary
                print(f"📊 Results for @{username}:")
                print(f"   Login popup: {'Yes' if login_popup else 'No'}")
                print(f"   Videos found: {'Yes' if videos_found else 'No'}")
                print(f"   CAPTCHA detected: {'Yes' if captcha_found else 'No'}")
                
                if not headless and (login_popup or captcha_found):
                    print("⏸️ Pausing for 10 seconds to see the page...")
                    await page.wait_for_timeout(10000)
                
            except Exception as e:
                print(f"❌ Error testing @{username}: {e}")
        
        await browser.close()
        
    except Exception as e:
        print(f"❌ Browser error: {e}")

async def check_login_popup(page):
    """Check for login popup"""
    login_selectors = [
        "text=Log in to TikTok",
        "text=Use QR code", 
        "div[role='dialog']",
        "div[data-e2e='login-modal']"
    ]
    
    for selector in login_selectors:
        try:
            element = await page.wait_for_selector(selector, timeout=2000)
            if element:
                print(f"🔐 Login popup detected: {selector}")
                return True
        except:
            continue
    
    return False

async def check_videos(page):
    """Check for videos on page"""
    video_selectors = [
        "div[data-e2e='user-post-item']",
        "div[data-e2e='user-post-item-list']",
        "a[href*='/video/']"
    ]
    
    for selector in video_selectors:
        try:
            elements = await page.query_selector_all(selector)
            if elements:
                print(f"🎥 Found {len(elements)} videos using: {selector}")
                return True
        except:
            continue
    
    return False

async def check_captcha(page):
    """Check for CAPTCHA"""
    captcha_selectors = [
        "text=Verify you are human",
        "text=Please complete the security check",
        "div[data-testid='captcha']",
        "iframe[src*='captcha']",
        ".captcha",
        "#captcha"
    ]
    
    for selector in captcha_selectors:
        try:
            element = await page.wait_for_selector(selector, timeout=2000)
            if element:
                print(f"🤖 CAPTCHA detected: {selector}")
                return True
        except:
            continue
    
    return False

async def test_user_agents(p, username):
    """Test different user agents"""
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1"
    ]
    
    url = f"https://www.tiktok.com/@{username}"
    
    for i, ua in enumerate(user_agents, 1):
        print(f"\n🔍 User Agent {i}: {ua[:50]}...")
        
        try:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(user_agent=ua)
            page = await context.new_page()
            
            await page.goto(url, timeout=30000)
            await page.wait_for_timeout(3000)
            
            title = await page.title()
            login_popup = await check_login_popup(page)
            videos_found = await check_videos(page)
            captcha_found = await check_captcha(page)
            
            print(f"   Title: {title}")
            print(f"   Login: {'Yes' if login_popup else 'No'}")
            print(f"   Videos: {'Yes' if videos_found else 'No'}")
            print(f"   CAPTCHA: {'Yes' if captcha_found else 'No'}")
            
            await browser.close()
            
        except Exception as e:
            print(f"   ❌ Error: {e}")

async def test_manual_navigation():
    """Test manual navigation with user interaction"""
    print("\n🔍 Manual Navigation Test")
    print("This will open a visible browser for you to see what happens")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        url = "https://www.tiktok.com/@its.sahiba2233"
        print(f"🌐 Opening {url}")
        
        await page.goto(url)
        
        print("👀 Browser opened. Check what you see:")
        print("   - Is there a login popup?")
        print("   - Is there a CAPTCHA?")
        print("   - Can you see videos?")
        print("   - Any other blocking elements?")
        
        input("\n⏸️ Press Enter when you're done observing...")
        
        await browser.close()

def main():
    """Main function"""
    print("🎯 TikTok Access Test Suite")
    print("=" * 50)
    
    print("Choose test type:")
    print("1. Automated tests (headless + visible)")
    print("2. Manual navigation test (visible browser)")
    print("3. Both")
    
    choice = input("\nEnter choice (1/2/3): ").strip()
    
    if choice == "1":
        asyncio.run(test_tiktok_access())
    elif choice == "2":
        asyncio.run(test_manual_navigation())
    elif choice == "3":
        asyncio.run(test_tiktok_access())
        asyncio.run(test_manual_navigation())
    else:
        print("❌ Invalid choice")
        return 1
    
    print("\n🎉 Tests completed!")
    print("\n💡 Based on results:")
    print("   - If login popup appears: TikTok requires login")
    print("   - If CAPTCHA appears: Need to solve CAPTCHA")
    print("   - If no videos: User might be private or blocked")
    print("   - Try different proxies if blocked")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        sys.exit(1)
