#!/usr/bin/env python3
# test_auto_proxy.py

"""
Test script for auto proxy functionality
"""

import sys
import time
from proxy_handler import (
    get_free_proxies_from_api, 
    get_auto_proxy, 
    update_proxy_file_with_api_proxies,
    is_proxy_valid
)

def test_free_proxy_apis():
    """Test fetching proxies from free APIs"""
    print("🔍 Testing Free Proxy APIs")
    print("=" * 50)
    
    proxies = get_free_proxies_from_api()
    print(f"📊 Total proxies fetched: {len(proxies)}")
    
    if proxies:
        print("\n📋 Sample proxies:")
        for i, proxy in enumerate(proxies[:5]):
            print(f"  {i+1}. {proxy}")
        
        if len(proxies) > 5:
            print(f"  ... and {len(proxies) - 5} more")
    
    return proxies

def test_proxy_validation(proxies, max_test=5):
    """Test proxy validation"""
    print(f"\n🧪 Testing Proxy Validation (max {max_test})")
    print("=" * 50)
    
    if not proxies:
        print("❌ No proxies to test")
        return []
    
    valid_proxies = []
    test_count = min(max_test, len(proxies))
    
    for i, proxy in enumerate(proxies[:test_count]):
        print(f"\n🔍 Testing proxy {i+1}/{test_count}: {proxy}")
        start_time = time.time()
        
        if is_proxy_valid(proxy):
            elapsed = time.time() - start_time
            print(f"✅ Valid proxy (took {elapsed:.2f}s): {proxy}")
            valid_proxies.append(proxy)
        else:
            elapsed = time.time() - start_time
            print(f"❌ Invalid proxy (took {elapsed:.2f}s): {proxy}")
    
    print(f"\n📊 Results: {len(valid_proxies)}/{test_count} proxies are valid")
    return valid_proxies

def test_auto_proxy():
    """Test auto proxy functionality"""
    print("\n🤖 Testing Auto Proxy")
    print("=" * 50)
    
    print("🔍 Getting auto proxy...")
    start_time = time.time()
    
    proxy = get_auto_proxy()
    elapsed = time.time() - start_time
    
    if proxy:
        print(f"✅ Auto proxy found (took {elapsed:.2f}s): {proxy}")
        return proxy
    else:
        print(f"❌ No auto proxy found (took {elapsed:.2f}s)")
        return None

def test_proxy_file_update():
    """Test updating proxy file with API proxies"""
    print("\n📝 Testing Proxy File Update")
    print("=" * 50)
    
    print("🔄 Updating proxy file with fresh proxies...")
    start_time = time.time()
    
    success = update_proxy_file_with_api_proxies()
    elapsed = time.time() - start_time
    
    if success:
        print(f"✅ Proxy file updated successfully (took {elapsed:.2f}s)")
        
        # Show file content
        try:
            with open("proxy.txt", "r") as f:
                lines = f.readlines()
                print(f"📄 Proxy file now has {len(lines)} lines")
                
                # Show first few lines
                print("\n📋 First 10 lines of proxy file:")
                for i, line in enumerate(lines[:10]):
                    print(f"  {i+1}. {line.strip()}")
                
                if len(lines) > 10:
                    print(f"  ... and {len(lines) - 10} more lines")
                    
        except Exception as e:
            print(f"❌ Error reading proxy file: {e}")
    else:
        print(f"❌ Failed to update proxy file (took {elapsed:.2f}s)")
    
    return success

def main():
    """Main test function"""
    print("🚀 TikTok Bot Auto Proxy Test")
    print("=" * 60)
    print()
    
    try:
        # Test 1: Fetch proxies from APIs
        proxies = test_free_proxy_apis()
        
        # Test 2: Validate some proxies
        if proxies:
            valid_proxies = test_proxy_validation(proxies, max_test=3)
        else:
            valid_proxies = []
        
        # Test 3: Test auto proxy functionality
        auto_proxy = test_auto_proxy()
        
        # Test 4: Test proxy file update
        file_updated = test_proxy_file_update()
        
        # Summary
        print("\n📊 Test Summary")
        print("=" * 50)
        print(f"✅ Proxies fetched from APIs: {len(proxies)}")
        print(f"✅ Valid proxies found: {len(valid_proxies)}")
        print(f"✅ Auto proxy working: {'Yes' if auto_proxy else 'No'}")
        print(f"✅ Proxy file updated: {'Yes' if file_updated else 'No'}")
        
        if auto_proxy:
            print(f"\n🎉 SUCCESS: Auto proxy is working!")
            print(f"🌐 Working proxy: {auto_proxy}")
        else:
            print(f"\n⚠️ WARNING: Auto proxy not working")
            print("💡 Suggestions:")
            print("  - Check internet connection")
            print("  - Try running again (APIs might be temporarily down)")
            print("  - Consider using premium proxy APIs")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
