#!/usr/bin/env python3
"""
Quick Start GUI
Script để khởi chạy GUI nhanh chóng
"""

import os
import sys
import subprocess
import webbrowser
import time
from threading import Thread

def check_and_install_dependencies():
    """Check and install required dependencies"""
    print("🔍 Checking dependencies...")
    
    required_packages = {
        'flask': 'flask==3.0.0',
        'flask_cors': 'flask-cors==4.0.0', 
        'requests': 'requests==2.31.0'
    }
    
    missing_packages = []
    
    for package, install_name in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package} - OK")
        except ImportError:
            missing_packages.append(install_name)
            print(f"❌ {package} - Missing")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages...")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    
    return True

def check_bot_files():
    """Check if bot files exist"""
    print("\n🔧 Checking bot files...")
    
    required_files = [
        'config.py', 'utils.py', 'scrape.py', 
        'telegram_logger.py', 'index.py', 'app.py'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - OK")
        else:
            missing_files.append(file)
            print(f"❌ {file} - Missing")
    
    if missing_files:
        print(f"\n⚠️ Missing files: {', '.join(missing_files)}")
        print("Please make sure all bot files are in the current directory")
        return False
    
    return True

def create_env_if_needed():
    """Create .env file if it doesn't exist"""
    if not os.path.exists('.env'):
        print("\n📝 Creating .env file...")
        
        env_content = """# TikTok Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_USER_ID=your_telegram_user_id_here
TIKTOK_USERNAME=your_tiktok_username
TIKTOK_PASSWORD=your_tiktok_password
TARGET_USERNAMES=its.sahiba2233,iamvirk,charlidamelio
RECHECK_INTERVAL_HOURS=2
SHARE_RULES=1000:10000:1,10000:50000:2,50000:999999999:3
"""
        
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print("✅ .env file created")
        print("⚠️ Please edit .env file with your actual credentials")
    else:
        print("✅ .env file exists")

def create_proxy_file_if_needed():
    """Create proxy.txt file if it doesn't exist"""
    if not os.path.exists('proxy.txt'):
        print("\n📝 Creating proxy.txt file...")
        
        proxy_content = """# Add your proxies here, one per line
# Format examples:
# http://proxy.example.com:8080
# http://username:<EMAIL>:8080
# socks5://proxy.example.com:1080

# Example proxies (replace with real ones):
# http://proxy1.example.com:8080
# http://user:<EMAIL>:3128
"""
        
        with open('proxy.txt', 'w') as f:
            f.write(proxy_content)
        
        print("✅ proxy.txt file created")
        print("⚠️ Please add your proxy servers to proxy.txt")
    else:
        print("✅ proxy.txt file exists")

def open_browser_delayed():
    """Open browser after a delay"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 Browser opened automatically")
    except Exception as e:
        print(f"⚠️ Could not open browser: {e}")

def start_gui():
    """Start the GUI"""
    print("\n🚀 Starting TikTok Bot GUI...")
    
    try:
        # Start browser opener in background
        browser_thread = Thread(target=open_browser_delayed, daemon=True)
        browser_thread.start()
        
        # Import and start Flask app
        from app import app
        
        print("📱 GUI will be available at: http://localhost:5000")
        print("🎯 Features available:")
        print("   - Dashboard: http://localhost:5000")
        print("   - Settings: http://localhost:5000/settings") 
        print("   - Logs: http://localhost:5000/logs")
        print("   - Videos: http://localhost:5000/videos")
        print("   - Proxy Manager: http://localhost:5000/proxy-manager")
        print("\n💡 Press Ctrl+C to stop the GUI")
        
        # Start Flask app
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n⏹️ GUI stopped by user")
    except Exception as e:
        print(f"\n💥 Failed to start GUI: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure all dependencies are installed")
        print("2. Check that port 5000 is not in use")
        print("3. Verify all bot files are present")
        return False
    
    return True

def main():
    """Main function"""
    print("🎨 TikTok Bot GUI Quick Start")
    print("=" * 50)
    
    # Check dependencies
    if not check_and_install_dependencies():
        print("\n❌ Dependency check failed")
        input("Press Enter to exit...")
        return 1
    
    # Check bot files
    if not check_bot_files():
        print("\n❌ Bot files check failed")
        input("Press Enter to exit...")
        return 1
    
    # Create config files if needed
    create_env_if_needed()
    create_proxy_file_if_needed()
    
    print("\n✅ All checks passed!")
    print("\n🎯 What you can do in the GUI:")
    print("   📊 Monitor bot status and statistics")
    print("   ⚙️ Configure TikTok and Telegram settings")
    print("   🎥 View processed videos and sharing history")
    print("   🌐 Manage proxy servers")
    print("   📋 View real-time logs")
    print("   🔧 Test TikTok access and troubleshoot issues")
    
    # Start GUI
    if not start_gui():
        print("\n❌ Failed to start GUI")
        input("Press Enter to exit...")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
