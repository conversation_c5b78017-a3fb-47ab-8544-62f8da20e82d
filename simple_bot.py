#!/usr/bin/env python3
# simple_bot.py

"""
Simple bot without emojis for testing
"""

import asyncio
from config import TARGET_USERNAMES, AUTO_PROXY_ENABLED
from telegram_logger import send_telegram_message
from utils import log_to_console

async def simple_bot():
    """Simple bot for testing"""
    try:
        log_to_console("Starting simple bot...")
        await send_telegram_message("Simple bot started")
        
        log_to_console(f"Target users: {TARGET_USERNAMES}")
        log_to_console(f"Auto proxy: {AUTO_PROXY_ENABLED}")
        
        await send_telegram_message(f"Target users: {TARGET_USERNAMES}")
        await send_telegram_message(f"Auto proxy: {AUTO_PROXY_ENABLED}")
        
        # Simulate some work
        for i in range(5):
            log_to_console(f"Working... step {i+1}/5")
            await send_telegram_message(f"Working... step {i+1}/5")
            await asyncio.sleep(2)
        
        log_to_console("Simple bot completed")
        await send_telegram_message("Simple bot completed successfully")
        
    except Exception as e:
        log_to_console(f"Simple bot failed: {e}")
        await send_telegram_message(f"Simple bot failed: {str(e)}")

if __name__ == "__main__":
    asyncio.run(simple_bot())
