#!/usr/bin/env python3
# bypass_captcha_scrape.py

"""
TikTok scrape with CAPTCHA bypass techniques
"""

import asyncio
import random
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
from config import TARGET_USERNAMES, AUTO_PROXY_ENABLED, PREMIUM_PROXY_API_KEY
from proxy_handler import get_auto_proxy
from telegram_logger import send_telegram_message, send_telegram_photo
from utils import (
    log_to_console,
    already_shared_videos, mark_video_shared, wait_with_retries,
    parse_like_count
)

def calculate_share_count_test(likes):
    """Test version of calculate_share_count with lower thresholds"""
    rules = [
        (0, 10, 1),      # 0-9 likes: 1 share
        (10, 50, 2),     # 10-49 likes: 2 shares  
        (50, 100, 5),    # 50-99 likes: 5 shares
        (100, 1000, 50), # 100-999 likes: 50 shares
        (1000, 5000, 100), # 1000-4999 likes: 100 shares
        (5000, float('inf'), 150) # 5000+ likes: 150 shares
    ]
    
    for min_likes, max_likes, shares in rules:
        if min_likes <= likes < max_likes:
            return shares
    return 0

async def human_like_delay():
    """Add human-like random delays"""
    delay = random.uniform(2, 5)  # 2-5 seconds
    await asyncio.sleep(delay)

async def setup_stealth_browser(p, proxy=None):
    """Setup browser with stealth settings to avoid detection"""
    
    # Use real Chrome user agent
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ]
    
    browser_args = [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-blink-features=AutomationControlled',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-extensions-except=/path/to/extension',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-images',  # Faster loading
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-default-apps',
        '--disable-popup-blocking',
        f'--user-agent={random.choice(user_agents)}'
    ]
    
    if proxy:
        browser_args.append(f'--proxy-server=http://{proxy}')
    
    browser = await p.chromium.launch(
        headless=True,
        args=browser_args
    )
    
    # Create context with additional stealth settings
    context = await browser.new_context(
        viewport={'width': 1920, 'height': 1080},
        user_agent=random.choice(user_agents),
        locale='en-US',
        timezone_id='America/New_York',
        permissions=['geolocation'],
        geolocation={'latitude': 40.7128, 'longitude': -74.0060},  # New York
        extra_http_headers={
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    )
    
    page = await context.new_page()
    
    # Add stealth scripts
    await page.add_init_script("""
        // Remove webdriver property
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // Mock plugins
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // Mock languages
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
        });
        
        // Mock permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
    """)
    
    return browser, page

async def handle_captcha(page):
    """Handle CAPTCHA if it appears"""
    try:
        # Check for common CAPTCHA indicators
        captcha_selectors = [
            '[data-testid="captcha"]',
            '.captcha',
            '#captcha',
            '[class*="captcha"]',
            '[id*="captcha"]',
            'iframe[src*="captcha"]',
            '.verify',
            '[class*="verify"]'
        ]
        
        for selector in captcha_selectors:
            captcha_element = await page.query_selector(selector)
            if captcha_element:
                log_to_console("🤖 CAPTCHA detected! Taking screenshot...")
                await send_telegram_message("🤖 CAPTCHA detected! Manual intervention needed.")
                
                # Take screenshot
                screenshot_path = "captcha_screenshot.png"
                await page.screenshot(path=screenshot_path, full_page=True)
                await send_telegram_photo(screenshot_path, "🤖 CAPTCHA Screenshot - Please solve manually")
                
                log_to_console("⏳ Waiting 60 seconds for manual CAPTCHA solving...")
                await asyncio.sleep(60)  # Wait for manual solving
                
                return True
        
        return False
        
    except Exception as e:
        log_to_console(f"[ERROR] Error checking CAPTCHA: {e}")
        return False

async def bypass_captcha_scrape():
    """Main scrape function with CAPTCHA bypass"""
    try:
        # Proxy setup
        if AUTO_PROXY_ENABLED:
            proxy = get_auto_proxy(PREMIUM_PROXY_API_KEY)
            if proxy:
                log_to_console(f"🌐 Using proxy: {proxy}")
                await send_telegram_message(f"🌐 Using proxy: {proxy}")
            else:
                log_to_console("❌ No proxy found")
                await send_telegram_message("❌ No proxy found")
                return
        else:
            proxy = None
            log_to_console("🌐 Proxy disabled - running without proxy")
            await send_telegram_message("🌐 Proxy disabled - running without proxy")

        async with async_playwright() as p:
            browser, page = await setup_stealth_browser(p, proxy)
            
            try:
                for username in TARGET_USERNAMES:
                    url = f"https://www.tiktok.com/@{username}"
                    
                    log_to_console(f"🔍 Processing @{username}")
                    await send_telegram_message(f"🔍 Processing @{username}")
                    
                    # Add random delay before navigation
                    await human_like_delay()
                    
                    # Navigate to user page
                    try:
                        response = await page.goto(url, wait_until='networkidle', timeout=30000)
                        log_to_console(f"✅ Loaded @{username} page (status: {response.status})")
                        
                        # Check for CAPTCHA
                        if await handle_captcha(page):
                            log_to_console("🤖 CAPTCHA handling completed, continuing...")
                        
                        await human_like_delay()
                        
                        # Try multiple video selectors
                        video_selectors = [
                            "div[data-e2e='user-post-item'] a",
                            "a[href*='/video/']",
                            "[data-e2e*='post'] a",
                            "div[class*='video'] a"
                        ]
                        
                        videos = []
                        for selector in video_selectors:
                            videos = await page.query_selector_all(selector)
                            if videos:
                                log_to_console(f"✅ Found {len(videos)} videos with selector: {selector}")
                                break
                        
                        if not videos:
                            log_to_console(f"❌ No videos found for @{username}")
                            await send_telegram_message(f"❌ No videos found for @{username}")
                            continue
                        
                        await send_telegram_message(f"📹 Found {len(videos)} videos for @{username}")
                        
                        # Collect video URLs
                        video_urls = []
                        for i, video in enumerate(videos[:3]):  # Process max 3 videos
                            try:
                                href = await video.get_attribute("href")
                                if href:
                                    if href.startswith("http"):
                                        video_url = href
                                    else:
                                        video_url = f"https://www.tiktok.com{href}"
                                    video_urls.append(video_url)
                            except Exception as e:
                                log_to_console(f"[ERROR] Error getting href for video {i+1}: {e}")
                                continue
                        
                        log_to_console(f"📋 Collected {len(video_urls)} video URLs")
                        
                        # Process videos
                        videos_processed = 0
                        videos_shared = 0
                        
                        for i, video_url in enumerate(video_urls):
                            try:
                                log_to_console(f"🎥 Processing video {i+1}: {video_url}")
                                
                                if already_shared_videos(video_url):
                                    log_to_console(f"⏭️ Video already shared, skipping")
                                    continue
                                
                                await human_like_delay()
                                
                                # Navigate to video
                                await page.goto(video_url, wait_until='networkidle', timeout=30000)
                                
                                # Check for CAPTCHA on video page
                                if await handle_captcha(page):
                                    log_to_console("🤖 CAPTCHA on video page handled")
                                
                                await human_like_delay()
                                videos_processed += 1
                                
                                # Get like count
                                if await wait_with_retries(page, "strong[data-e2e='like-count']", timeout=10000):
                                    like_text = await page.locator("strong[data-e2e='like-count']").inner_text()
                                    likes = parse_like_count(like_text)
                                    log_to_console(f"👍 Video has {likes} likes")
                                    
                                    share_count = calculate_share_count_test(likes)
                                    if share_count > 0:
                                        log_to_console(f"📤 Video qualifies for {share_count} shares")
                                        # For now, just mark as shared without actually clicking
                                        # (to avoid more CAPTCHA triggers)
                                        mark_video_shared(video_url, share_count)
                                        videos_shared += 1
                                        await send_telegram_message(f"📢 Marked {video_url} for {share_count} shares")
                                    else:
                                        log_to_console(f"[INFO] Video doesn't meet share criteria ({likes} likes)")
                                else:
                                    log_to_console(f"[WARNING] Could not find like count for {video_url}")
                                
                            except Exception as video_error:
                                log_to_console(f"[ERROR] Error processing video {i+1}: {video_error}")
                                continue
                        
                        log_to_console(f"✅ Completed @{username}: {videos_processed} processed, {videos_shared} shared")
                        await send_telegram_message(f"✅ @{username}: {videos_processed} processed, {videos_shared} shared")
                        
                    except PlaywrightTimeoutError:
                        log_to_console(f"[TIMEOUT] Could not load {url}")
                        await send_telegram_message(f"⏰ Timeout loading @{username}")
                    except Exception as e:
                        log_to_console(f"[ERROR] Exception for @{username}: {e}")
                        try:
                            page_path = f"error_{username}.png"
                            await page.screenshot(path=page_path)
                            await send_telegram_photo(page_path, f"❌ Error for @{username}: {str(e)[:100]}")
                        except:
                            pass
                
            finally:
                await browser.close()
                
        log_to_console("✅ Bypass CAPTCHA scrape completed")
        await send_telegram_message("✅ Bypass CAPTCHA scrape completed")
        
    except Exception as e:
        log_to_console(f"[ERROR] Bypass CAPTCHA scrape failed: {e}")
        await send_telegram_message(f"❌ Bypass CAPTCHA scrape failed: {str(e)[:100]}")

if __name__ == "__main__":
    asyncio.run(bypass_captcha_scrape())
