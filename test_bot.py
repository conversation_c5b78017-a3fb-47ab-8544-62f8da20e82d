#!/usr/bin/env python3
"""
Test script for TikTok Bot
Kiể<PERSON> tra các thành phần chính của bot
"""

import asyncio
import sys
import traceback

def test_imports():
    """Test all imports"""
    print("🔍 Testing imports...")
    
    try:
        import config
        print("✅ config.py - OK")
    except Exception as e:
        print(f"❌ config.py - ERROR: {e}")
        return False
    
    try:
        import utils
        print("✅ utils.py - OK")
    except Exception as e:
        print(f"❌ utils.py - ERROR: {e}")
        return False
    
    try:
        import telegram_logger
        print("✅ telegram_logger.py - OK")
    except Exception as e:
        print(f"❌ telegram_logger.py - ERROR: {e}")
        return False
    
    try:
        import proxy_handler
        print("✅ proxy_handler.py - OK")
    except Exception as e:
        print(f"❌ proxy_handler.py - ERROR: {e}")
        return False
    
    try:
        from playwright.async_api import async_playwright
        print("✅ playwright - OK")
    except Exception as e:
        print(f"❌ playwright - ERROR: {e}")
        print("💡 Run: pip install playwright && playwright install chromium")
        return False
    
    return True

def test_config():
    """Test configuration"""
    print("\n🔍 Testing configuration...")
    
    try:
        from config import (
            TELEGRAM_BOT_TOKEN, TELEGRAM_USER_ID,
            TARGET_USERNAMES, SHARE_RULES
        )
        
        print(f"✅ Telegram Token: {'Set' if TELEGRAM_BOT_TOKEN else 'Not set'}")
        print(f"✅ Telegram User ID: {TELEGRAM_USER_ID}")
        print(f"✅ Target usernames: {TARGET_USERNAMES}")
        print(f"✅ Share rules: {len(SHARE_RULES)} rules")
        
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_utils():
    """Test utility functions"""
    print("\n🔍 Testing utility functions...")
    
    try:
        from utils import (
            parse_like_count, calculate_share_count,
            get_target_usernames, log_to_console
        )
        
        # Test parse_like_count
        test_cases = [
            ("1.2K", 1200),
            ("3.5M", 3500000),
            ("500", 500),
            ("15.7K", 15700),
            ("2M", 2000000)
        ]
        
        for input_val, expected in test_cases:
            result = parse_like_count(input_val)
            if result == expected:
                print(f"✅ parse_like_count('{input_val}') = {result}")
            else:
                print(f"❌ parse_like_count('{input_val}') = {result}, expected {expected}")
                return False
        
        # Test calculate_share_count
        share_tests = [
            (50, 0),      # < 100 likes
            (500, 50),    # 100-999 likes
            (2000, 100),  # 1000-4999 likes
            (10000, 150)  # 5000+ likes
        ]
        
        for likes, expected_shares in share_tests:
            result = calculate_share_count(likes)
            if result == expected_shares:
                print(f"✅ calculate_share_count({likes}) = {result}")
            else:
                print(f"❌ calculate_share_count({likes}) = {result}, expected {expected_shares}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Utils test error: {e}")
        traceback.print_exc()
        return False

async def test_telegram():
    """Test Telegram connection"""
    print("\n🔍 Testing Telegram connection...")
    
    try:
        from telegram_logger import send_telegram_message
        from config import TELEGRAM_BOT_TOKEN, TELEGRAM_USER_ID
        
        if not TELEGRAM_BOT_TOKEN or not TELEGRAM_USER_ID:
            print("⚠️ Telegram credentials not configured")
            return True
        
        print("📱 Sending test message...")
        await send_telegram_message("🤖 TikTok Bot test message - All systems OK!")
        print("✅ Telegram message sent successfully")
        return True
        
    except Exception as e:
        print(f"❌ Telegram test error: {e}")
        return False

async def test_proxy():
    """Test proxy functionality"""
    print("\n🔍 Testing proxy functionality...")
    
    try:
        from proxy_handler import load_proxies, get_valid_proxy
        
        proxies = load_proxies()
        print(f"📋 Loaded {len(proxies)} proxies")
        
        if proxies:
            print("🔍 Testing first proxy...")
            # Don't test all proxies in test mode, just check loading
            print("✅ Proxy loading works")
        else:
            print("⚠️ No proxies found - bot will run without proxy")
        
        return True
        
    except Exception as e:
        print(f"❌ Proxy test error: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 TikTok Bot Test Suite")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Utilities", test_utils),
        ("Telegram", test_telegram),
        ("Proxy", test_proxy)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:15} {status}")
        if result:
            passed += 1
    
    print(f"\nResult: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Bot is ready to run.")
        print("💡 Run: python index.py")
    else:
        print("\n⚠️ Some tests failed. Please fix issues before running bot.")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        traceback.print_exc()
        sys.exit(1)
