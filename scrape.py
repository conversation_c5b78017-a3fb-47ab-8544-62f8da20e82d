# scrape.py

import asyncio
import traceback
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
from proxy_handler import get_valid_proxy
from telegram_logger import send_telegram_message, send_telegram_photo
from utils import (
    get_target_usernames, calculate_share_count, log_to_console,
    already_shared_videos, mark_video_shared, wait_with_retries,
    parse_like_count
)

RECHECK_INTERVAL = 2 * 60 * 60  # 2 hours

async def share_video(video_url, share_count, page):
    """Share video by clicking the share button multiple times"""
    success_count = 0

    try:
        # Navigate to video
        await page.goto(video_url, timeout=30000)
        await page.wait_for_timeout(2000)

        for i in range(share_count):
            try:
                # Look for share button with multiple possible selectors
                share_selectors = [
                    "button[data-e2e='share-button']",
                    "button[aria-label*='Share']",
                    "button[title*='Share']",
                    "div[data-e2e='share-button']",
                    "span[data-e2e='share-button']"
                ]

                share_button = None
                for selector in share_selectors:
                    try:
                        share_button = await page.wait_for_selector(selector, timeout=3000)
                        if share_button:
                            break
                    except:
                        continue

                if share_button:
                    # Click share button
                    await share_button.click()
                    await page.wait_for_timeout(1000)

                    # Try to close any share modal that might appear
                    try:
                        close_selectors = [
                            "button[aria-label*='Close']",
                            "button[data-e2e='close-button']",
                            ".close-button",
                            "[data-testid='close-button']"
                        ]
                        for close_selector in close_selectors:
                            close_btn = await page.query_selector(close_selector)
                            if close_btn:
                                await close_btn.click()
                                break
                    except:
                        pass

                    success_count += 1
                    log_to_console(f"✅ Shared {video_url} ({success_count}/{share_count})")

                    # Wait between shares to avoid rate limiting
                    if i < share_count - 1:
                        await page.wait_for_timeout(2000)
                else:
                    log_to_console(f"[WARNING] Share button not found for {video_url}")
                    break

            except Exception as e:
                log_to_console(f"[ERROR] Failed to share {video_url} (attempt {i+1}): {e}")
                # Continue trying other shares
                continue

    except Exception as e:
        log_to_console(f"[ERROR] Failed to load video {video_url}: {e}")

    return success_count

async def scrape():
    while True:
        try:
            proxy = get_valid_proxy()
            proxy_text = proxy if proxy else "None"
            log_to_console(f"🌐 Using proxy: {proxy_text}")
            await send_telegram_message(f"🌐 Using proxy: {proxy_text}")

            async with async_playwright() as p:
                browser = await p.chromium.launch(
                    proxy={"server": proxy} if proxy else None,
                    headless=True
                )
                context = await browser.new_context()
                page = await context.new_page()

                for username in get_target_usernames():
                    url = f"https://www.tiktok.com/@{username}"
                    try:
                        await page.goto(url, timeout=60000)
                        await page.wait_for_selector("div[data-e2e='user-post-item']", timeout=10000)

                        videos = await page.query_selector_all("div[data-e2e='user-post-item'] a")

                        for video in videos:
                            href = await video.get_attribute("href")
                            video_url = f"https://www.tiktok.com{href}"
                            if already_shared_videos(video_url):
                                continue

                            await page.goto(video_url)

                            # Wait for like count with retries
                            if not await wait_with_retries(page, "strong[data-e2e='like-count']", timeout=15000):
                                log_to_console(f"[WARNING] Could not find like count for {video_url}")
                                continue

                            like_text = await page.locator("strong[data-e2e='like-count']").inner_text()
                            likes = parse_like_count(like_text)
                            log_to_console(f"[INFO] Video {video_url} has {likes} likes")

                            share_count = calculate_share_count(likes)
                            if share_count > 0:
                                actual_shares = await share_video(video_url, share_count, page)
                                if actual_shares > 0:
                                    mark_video_shared(video_url, actual_shares)
                                    await send_telegram_message(f"📢 Shared {video_url} with {actual_shares}/{share_count} shares.")
                                else:
                                    log_to_console(f"[WARNING] Failed to share {video_url}")
                            else:
                                log_to_console(f"[INFO] Video {video_url} doesn't meet share criteria ({likes} likes)")
                    except PlaywrightTimeoutError:
                        log_to_console(f"[TIMEOUT] Could not load {url}")
                        await send_telegram_message(f"⏰ Timeout loading {username}")
                    except Exception as e:
                        log_to_console(f"[ERROR] Exception for {username}: {e}")
                        try:
                            page_path = f"screenshot_{username}.png"
                            await page.screenshot(path=page_path)
                            await send_telegram_photo(page_path, f"❌ Error for {username}: {str(e)[:100]}")
                        except Exception as screenshot_error:
                            log_to_console(f"[ERROR] Failed to take screenshot: {screenshot_error}")
                            await send_telegram_message(f"❌ Error for {username}: {str(e)[:200]}")
                await browser.close()

        except Exception as e:
            error_msg = f"[FATAL] {e}\n{traceback.format_exc()}"
            log_to_console(error_msg)
            try:
                await send_telegram_message(f"❌ Bot crashed:\n<pre>{traceback.format_exc()[:3000]}</pre>")
            except Exception as telegram_error:
                log_to_console(f"[ERROR] Failed to send crash report to Telegram: {telegram_error}")

        log_to_console(f"⏳ Sleeping {RECHECK_INTERVAL//60} minutes before next round")
        await asyncio.sleep(RECHECK_INTERVAL)