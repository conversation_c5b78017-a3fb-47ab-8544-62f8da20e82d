# scrape.py

import asyncio
import traceback
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
from proxy_handler import get_auto_proxy
from telegram_logger import send_telegram_message, send_telegram_photo
from utils import (
    get_target_usernames, calculate_share_count, log_to_console,
    already_shared_videos, mark_video_shared, wait_with_retries,
    parse_like_count
)
from config import AUTO_PROXY_ENABLED, PREMIUM_PROXY_API_KEY

RECHECK_INTERVAL = 2 * 60 * 60  # 2 hours

async def share_video(video_url, share_count, page):
    """Share video by clicking the share button multiple times"""
    success_count = 0

    try:
        # Navigate to video
        await page.goto(video_url, timeout=30000)
        await page.wait_for_timeout(2000)

        for i in range(share_count):
            try:
                # Look for share button with multiple possible selectors
                share_selectors = [
                    "button[data-e2e='share-button']",
                    "button[aria-label*='Share']",
                    "button[title*='Share']",
                    "div[data-e2e='share-button']",
                    "span[data-e2e='share-button']"
                ]

                share_button = None
                for selector in share_selectors:
                    try:
                        share_button = await page.wait_for_selector(selector, timeout=3000)
                        if share_button:
                            break
                    except:
                        continue

                if share_button:
                    # Click share button
                    await share_button.click()
                    await page.wait_for_timeout(1000)

                    # Try to close any share modal that might appear
                    try:
                        close_selectors = [
                            "button[aria-label*='Close']",
                            "button[data-e2e='close-button']",
                            ".close-button",
                            "[data-testid='close-button']"
                        ]
                        for close_selector in close_selectors:
                            close_btn = await page.query_selector(close_selector)
                            if close_btn:
                                await close_btn.click()
                                break
                    except:
                        pass

                    success_count += 1
                    log_to_console(f"✅ Shared {video_url} ({success_count}/{share_count})")

                    # Wait between shares to avoid rate limiting
                    if i < share_count - 1:
                        await page.wait_for_timeout(2000)
                else:
                    log_to_console(f"[WARNING] Share button not found for {video_url}")
                    break

            except Exception as e:
                log_to_console(f"[ERROR] Failed to share {video_url} (attempt {i+1}): {e}")
                # Continue trying other shares
                continue

    except Exception as e:
        log_to_console(f"[ERROR] Failed to load video {video_url}: {e}")

    return success_count

async def handle_tiktok_login_popup(page):
    """Handle TikTok login popup if it appears"""
    try:
        log_to_console("🔍 Checking for login popup...")

        # Wait a bit for page to load
        await page.wait_for_timeout(3000)

        # Check for login popup/modal
        login_indicators = [
            "text=Log in to TikTok",
            "text=Use QR code",
            "div[role='dialog']",
            "div[data-e2e='login-modal']"
        ]

        popup_found = False
        for indicator in login_indicators:
            try:
                element = await page.wait_for_selector(indicator, timeout=2000)
                if element:
                    log_to_console(f"🔐 Login popup detected: {indicator}")
                    popup_found = True
                    break
            except:
                continue

        if popup_found:
            # Try to close the popup
            close_methods = [
                # Method 1: Close button
                ("button[aria-label*='Close']", "close button"),
                ("button[data-e2e='close-button']", "close button"),
                ("svg[data-e2e='close-icon']", "close icon"),
                ("button:has-text('×')", "X button"),

                # Method 2: Click outside modal
                ("body", "click outside"),

                # Method 3: Press Escape
                ("escape", "escape key")
            ]

            for method, description in close_methods:
                try:
                    if method == "escape":
                        await page.keyboard.press('Escape')
                        log_to_console(f"⌨️ Pressed Escape key")
                    elif method == "body":
                        await page.click('body', position={'x': 50, 'y': 50})
                        log_to_console(f"🖱️ Clicked outside modal")
                    else:
                        close_btn = await page.wait_for_selector(method, timeout=2000)
                        if close_btn:
                            await close_btn.click()
                            log_to_console(f"✅ Closed popup using {description}")

                    await page.wait_for_timeout(2000)

                    # Check if popup is gone
                    popup_still_there = False
                    for indicator in login_indicators:
                        try:
                            element = await page.wait_for_selector(indicator, timeout=1000)
                            if element:
                                popup_still_there = True
                                break
                        except:
                            continue

                    if not popup_still_there:
                        log_to_console("✅ Login popup successfully closed")
                        return True

                except Exception as e:
                    log_to_console(f"[WARNING] Failed to close popup with {description}: {e}")
                    continue

            log_to_console("⚠️ Could not close login popup, continuing anyway...")
            return False
        else:
            log_to_console("✅ No login popup detected")
            return True

    except Exception as e:
        log_to_console(f"[ERROR] Error handling login popup: {e}")
        return False

async def check_page_accessibility(page, url, username):
    """Check if TikTok page is accessible"""
    try:
        log_to_console(f"🔍 Checking accessibility for @{username}")

        # Navigate to page
        await page.goto(url, timeout=30000)

        # Handle login popup
        await handle_tiktok_login_popup(page)

        # Wait for page content
        await page.wait_for_timeout(5000)

        # Check for various page states
        page_states = [
            # Success indicators
            ("div[data-e2e='user-post-item']", "videos found", True),
            ("div[data-e2e='user-tab-videos']", "videos tab found", True),
            ("h1[data-e2e='user-title']", "user title found", True),

            # Error indicators
            ("text=User not found", "user not found", False),
            ("text=This account is private", "private account", False),
            ("text=Something went wrong", "error page", False),
            ("text=Couldn't load page", "load error", False),
        ]

        for selector, description, is_success in page_states:
            try:
                element = await page.wait_for_selector(selector, timeout=3000)
                if element:
                    if is_success:
                        log_to_console(f"✅ Page accessible: {description}")
                        return True
                    else:
                        log_to_console(f"❌ Page not accessible: {description}")
                        return False
            except:
                continue

        # If no clear indicators, check page title and content
        title = await page.title()
        if "TikTok" in title and "error" not in title.lower():
            log_to_console(f"✅ Page seems accessible (title: {title})")
            return True
        else:
            log_to_console(f"⚠️ Uncertain page state (title: {title})")
            return True  # Continue anyway

    except Exception as e:
        log_to_console(f"[ERROR] Failed to check page accessibility: {e}")
        return False

async def scrape():
    while True:
        try:
            # Try to get a working proxy automatically
            if AUTO_PROXY_ENABLED:
                proxy = get_auto_proxy(PREMIUM_PROXY_API_KEY)
                proxy_text = proxy if proxy else "None"
                log_to_console(f"🌐 Using proxy: {proxy_text}")
                await send_telegram_message(f"🌐 Using proxy: {proxy_text}")
            else:
                proxy = None
                log_to_console(f"🌐 Proxy disabled - running without proxy")
                await send_telegram_message(f"🌐 Proxy disabled - running without proxy")

            async with async_playwright() as p:
                # Launch browser with anti-detection settings
                browser = await p.chromium.launch(
                    proxy={"server": proxy} if proxy else None,
                    headless=True,
                    args=[
                        '--no-sandbox',
                        '--disable-blink-features=AutomationControlled',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor',
                        '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                    ]
                )
                context = await browser.new_context()
                page = await context.new_page()

                # Set additional headers
                await page.set_extra_http_headers({
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
                })

                for username in get_target_usernames():
                    url = f"https://www.tiktok.com/@{username}"
                    try:
                        log_to_console(f"🔍 Processing @{username}")

                        # Check if page is accessible
                        if not await check_page_accessibility(page, url, username):
                            log_to_console(f"❌ Cannot access @{username}, skipping...")
                            await send_telegram_message(f"❌ Cannot access @{username} - may need different proxy or login")
                            continue

                        # Try to find videos
                        try:
                            await page.wait_for_selector("div[data-e2e='user-post-item']", timeout=10000)
                        except:
                            log_to_console(f"⚠️ No videos found for @{username}, trying alternative selectors...")

                            # Try alternative selectors
                            alternative_selectors = [
                                "div[data-e2e='user-post-item-list']",
                                "div[data-e2e='user-post']",
                                "a[href*='/video/']"
                            ]

                            videos_found = False
                            for selector in alternative_selectors:
                                try:
                                    await page.wait_for_selector(selector, timeout=5000)
                                    videos_found = True
                                    log_to_console(f"✅ Found videos using alternative selector: {selector}")
                                    break
                                except:
                                    continue

                            if not videos_found:
                                log_to_console(f"❌ No videos found for @{username} with any selector")
                                continue

                        videos = await page.query_selector_all("div[data-e2e='user-post-item'] a")

                        for video in videos:
                            href = await video.get_attribute("href")
                            video_url = f"https://www.tiktok.com{href}"
                            if already_shared_videos(video_url):
                                continue

                            await page.goto(video_url)

                            # Wait for like count with retries
                            if not await wait_with_retries(page, "strong[data-e2e='like-count']", timeout=15000):
                                log_to_console(f"[WARNING] Could not find like count for {video_url}")
                                continue

                            like_text = await page.locator("strong[data-e2e='like-count']").inner_text()
                            likes = parse_like_count(like_text)
                            log_to_console(f"[INFO] Video {video_url} has {likes} likes")

                            share_count = calculate_share_count(likes)
                            if share_count > 0:
                                actual_shares = await share_video(video_url, share_count, page)
                                if actual_shares > 0:
                                    mark_video_shared(video_url, actual_shares)
                                    await send_telegram_message(f"📢 Shared {video_url} with {actual_shares}/{share_count} shares.")
                                else:
                                    log_to_console(f"[WARNING] Failed to share {video_url}")
                            else:
                                log_to_console(f"[INFO] Video {video_url} doesn't meet share criteria ({likes} likes)")
                    except PlaywrightTimeoutError:
                        log_to_console(f"[TIMEOUT] Could not load {url}")
                        await send_telegram_message(f"⏰ Timeout loading {username}")
                    except Exception as e:
                        log_to_console(f"[ERROR] Exception for {username}: {e}")
                        try:
                            page_path = f"screenshot_{username}.png"
                            await page.screenshot(path=page_path)
                            await send_telegram_photo(page_path, f"❌ Error for {username}: {str(e)[:100]}")
                        except Exception as screenshot_error:
                            log_to_console(f"[ERROR] Failed to take screenshot: {screenshot_error}")
                            await send_telegram_message(f"❌ Error for {username}: {str(e)[:200]}")
                await browser.close()

        except Exception as e:
            error_msg = f"[FATAL] {e}\n{traceback.format_exc()}"
            log_to_console(error_msg)
            try:
                await send_telegram_message(f"❌ Bot crashed:\n<pre>{traceback.format_exc()[:3000]}</pre>")
            except Exception as telegram_error:
                log_to_console(f"[ERROR] Failed to send crash report to Telegram: {telegram_error}")

        log_to_console(f"⏳ Sleeping {RECHECK_INTERVAL//60} minutes before next round")
        await asyncio.sleep(RECHECK_INTERVAL)