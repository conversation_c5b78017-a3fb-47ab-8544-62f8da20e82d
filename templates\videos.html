{% extends "base.html" %}

{% block title %}Videos - TikTok Bot Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-video me-2"></i>Video History</h2>
                <p class="text-muted">Track all videos processed by your bot</p>
            </div>
            <div>
                <button class="btn btn-outline-primary me-2" onclick="refreshVideos()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
                <button class="btn btn-outline-success" onclick="exportVideos()">
                    <i class="fas fa-download me-1"></i>Export
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- Summary Cards -->
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-video fa-2x text-primary mb-2"></i>
                <h4 id="total-videos">{{ videos|length }}</h4>
                <p class="card-text">Total Videos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-share fa-2x text-success mb-2"></i>
                <h4 id="total-shares">
                    {% set total_shares = videos.values() | map(attribute='shared_count') | map('default', 0) | sum %}
                    {{ total_shares }}
                </h4>
                <p class="card-text">Total Shares</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                <h4 id="today-videos">0</h4>
                <p class="card-text">Today's Videos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                <h4 id="avg-shares">0</h4>
                <p class="card-text">Avg Shares</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Video List
                            <span id="video-count" class="badge bg-secondary ms-2">{{ videos|length }}</span>
                        </h5>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end">
                            <!-- Search -->
                            <input type="text" class="form-control form-control-sm me-2" 
                                   id="search-videos" placeholder="Search videos..." style="width: 200px;">
                            
                            <!-- Filter -->
                            <select class="form-select form-select-sm" id="filter-videos" style="width: auto;">
                                <option value="all">All Videos</option>
                                <option value="shared">Shared Only</option>
                                <option value="not-shared">Not Shared</option>
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                {% if videos %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="videos-table">
                        <thead class="table-light">
                            <tr>
                                <th>Video URL</th>
                                <th>Shares</th>
                                <th>Timestamp</th>
                                <th>Last Shared</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for url, data in videos.items() %}
                            <tr data-video-url="{{ url }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fab fa-tiktok text-tiktok me-2"></i>
                                        <div>
                                            <a href="{{ url }}" target="_blank" class="text-decoration-none">
                                                {{ url|truncate(50) }}
                                            </a>
                                            <br>
                                            <small class="text-muted">
                                                {% set username = url.split('@')[1].split('/')[0] if '@' in url else 'Unknown' %}
                                                @{{ username }}
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{% if data.shared_count > 0 %}success{% else %}secondary{% endif %} fs-6">
                                        {{ data.shared_count or 0 }}
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ data.timestamp[:19] if data.timestamp else 'Unknown' }}
                                    </small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ data.last_shared[:19] if data.last_shared else 'Never' }}
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="openVideo('{{ url }}')" 
                                                title="Open Video">
                                            <i class="fas fa-external-link-alt"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="copyVideoUrl('{{ url }}')" 
                                                title="Copy URL">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="removeVideo('{{ url }}')" 
                                                title="Remove">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-video fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No videos found</h5>
                    <p class="text-muted">Videos will appear here once the bot starts processing</p>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                        <i class="fas fa-play me-2"></i>Start Bot
                    </a>
                </div>
                {% endif %}
            </div>
            {% if videos %}
            <div class="card-footer">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <small class="text-muted">
                            Showing <span id="showing-count">{{ videos|length }}</span> of 
                            <span id="total-count">{{ videos|length }}</span> videos
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <nav>
                            <ul class="pagination pagination-sm mb-0" id="pagination">
                                <!-- Pagination will be generated by JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Video Details Modal -->
<div class="modal fade" id="videoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Video Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="video-details">
                    <!-- Video details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="openCurrentVideo()">
                    <i class="fas fa-external-link-alt me-2"></i>Open Video
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let videosData = {{ videos|tojson }};
let filteredVideos = videosData;
let currentPage = 1;
let itemsPerPage = 20;

$(document).ready(function() {
    calculateStatistics();
    setupEventListeners();
    filterVideos();
});

function calculateStatistics() {
    const videos = Object.values(videosData);
    const today = new Date().toISOString().split('T')[0];
    
    // Today's videos
    const todayVideos = videos.filter(v => 
        v.timestamp && v.timestamp.startsWith(today)
    ).length;
    $('#today-videos').text(todayVideos);
    
    // Average shares
    const totalShares = videos.reduce((sum, v) => sum + (v.shared_count || 0), 0);
    const avgShares = videos.length > 0 ? Math.round(totalShares / videos.length) : 0;
    $('#avg-shares').text(avgShares);
}

function setupEventListeners() {
    $('#search-videos').on('input', debounce(filterVideos, 300));
    $('#filter-videos').on('change', filterVideos);
}

function filterVideos() {
    const searchTerm = $('#search-videos').val().toLowerCase();
    const filterType = $('#filter-videos').val();
    
    filteredVideos = Object.entries(videosData).filter(([url, data]) => {
        // Search filter
        if (searchTerm && !url.toLowerCase().includes(searchTerm)) {
            return false;
        }
        
        // Type filter
        switch(filterType) {
            case 'shared':
                return data.shared_count > 0;
            case 'not-shared':
                return !data.shared_count || data.shared_count === 0;
            case 'today':
                const today = new Date().toISOString().split('T')[0];
                return data.timestamp && data.timestamp.startsWith(today);
            case 'week':
                const weekAgo = new Date();
                weekAgo.setDate(weekAgo.getDate() - 7);
                return data.timestamp && new Date(data.timestamp) >= weekAgo;
            default:
                return true;
        }
    });
    
    updateTable();
    updatePagination();
}

function updateTable() {
    const tbody = $('#videos-table tbody');
    tbody.empty();
    
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const pageVideos = filteredVideos.slice(start, end);
    
    if (pageVideos.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="5" class="text-center py-4">
                    <i class="fas fa-search fa-2x text-muted mb-2"></i>
                    <div class="text-muted">No videos match your search criteria</div>
                </td>
            </tr>
        `);
        return;
    }
    
    pageVideos.forEach(([url, data]) => {
        const username = url.includes('@') ? url.split('@')[1].split('/')[0] : 'Unknown';
        const sharesBadge = data.shared_count > 0 ? 'success' : 'secondary';
        
        tbody.append(`
            <tr data-video-url="${url}">
                <td>
                    <div class="d-flex align-items-center">
                        <i class="fab fa-tiktok text-tiktok me-2"></i>
                        <div>
                            <a href="${url}" target="_blank" class="text-decoration-none">
                                ${truncateText(url, 50)}
                            </a>
                            <br>
                            <small class="text-muted">@${username}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${sharesBadge} fs-6">
                        ${data.shared_count || 0}
                    </span>
                </td>
                <td>
                    <small class="text-muted">
                        ${data.timestamp ? data.timestamp.substring(0, 19) : 'Unknown'}
                    </small>
                </td>
                <td>
                    <small class="text-muted">
                        ${data.last_shared ? data.last_shared.substring(0, 19) : 'Never'}
                    </small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="openVideo('${url}')" title="Open Video">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="copyVideoUrl('${url}')" title="Copy URL">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="removeVideo('${url}')" title="Remove">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
    });
    
    // Update counters
    $('#showing-count').text(pageVideos.length);
    $('#total-count').text(filteredVideos.length);
    $('#video-count').text(filteredVideos.length);
}

function updatePagination() {
    const totalPages = Math.ceil(filteredVideos.length / itemsPerPage);
    const pagination = $('#pagination');
    pagination.empty();
    
    if (totalPages <= 1) return;
    
    // Previous button
    pagination.append(`
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>
        </li>
    `);
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            pagination.append(`
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `);
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            pagination.append('<li class="page-item disabled"><span class="page-link">...</span></li>');
        }
    }
    
    // Next button
    pagination.append(`
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>
        </li>
    `);
}

function changePage(page) {
    const totalPages = Math.ceil(filteredVideos.length / itemsPerPage);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    updateTable();
    updatePagination();
}

function openVideo(url) {
    window.open(url, '_blank');
}

function copyVideoUrl(url) {
    navigator.clipboard.writeText(url).then(function() {
        showAlert('success', 'Video URL copied to clipboard');
    }).catch(function() {
        showAlert('error', 'Failed to copy URL');
    });
}

function removeVideo(url) {
    if (confirm('Are you sure you want to remove this video from history?')) {
        // This would need backend implementation
        showAlert('warning', 'Remove video functionality needs backend implementation');
    }
}

function refreshVideos() {
    location.reload();
}

function exportVideos() {
    const csvContent = generateCSV();
    downloadFile(csvContent, `tiktok-videos-${new Date().toISOString().split('T')[0]}.csv`, 'text/csv');
    showAlert('success', 'Videos exported successfully');
}

function generateCSV() {
    let csv = 'URL,Username,Shares,Timestamp,Last Shared\n';
    
    Object.entries(videosData).forEach(([url, data]) => {
        const username = url.includes('@') ? url.split('@')[1].split('/')[0] : 'Unknown';
        csv += `"${url}","${username}","${data.shared_count || 0}","${data.timestamp || ''}","${data.last_shared || ''}"\n`;
    });
    
    return csv;
}

function truncateText(text, length) {
    return text.length > length ? text.substring(0, length) + '...' : text;
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showAlert(type, message) {
    // Use the global function from app.js
    if (window.TikTokBot && window.TikTokBot.showAlert) {
        window.TikTokBot.showAlert(type, message);
    }
}

function downloadFile(content, filename, contentType) {
    // Use the global function from app.js
    if (window.TikTokBot && window.TikTokBot.downloadFile) {
        window.TikTokBot.downloadFile(content, filename, contentType);
    }
}
</script>
{% endblock %}
