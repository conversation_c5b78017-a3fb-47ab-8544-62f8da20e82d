/* TikTok Bot Manager Custom Styles */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --tiktok-color: #ff0050;
}

/* Global Styles */
body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand .fab.fa-tiktok {
    color: var(--tiktok-color);
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: none;
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* Status Indicators */
.status-running {
    color: var(--success-color);
    animation: pulse 2s infinite;
}

.status-stopped {
    color: var(--danger-color);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Statistics Cards */
.stats-card {
    text-align: center;
    padding: 2rem 1rem;
}

.stats-card .fa-2x {
    margin-bottom: 1rem;
}

.stats-card h4 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* Target Users List */
.target-user-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease-in-out;
}

.target-user-item:hover {
    background-color: #f8f9fa;
    border-color: var(--primary-color);
}

.target-user-item .fab.fa-tiktok {
    color: var(--tiktok-color);
}

/* Logs Container */
.logs-container {
    background-color: #1e1e1e;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

.log-entry {
    border-left: 3px solid transparent;
    transition: background-color 0.2s ease-in-out;
}

.log-entry:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.log-entry.log-error {
    border-left-color: var(--danger-color);
}

.log-entry.log-warning {
    border-left-color: var(--warning-color);
}

.log-entry.log-success {
    border-left-color: var(--success-color);
}

.log-entry.log-info {
    border-left-color: var(--info-color);
}

/* Form Enhancements */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Badge Enhancements */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-success {
    border-left-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.1);
}

.alert-danger {
    border-left-color: var(--danger-color);
    background-color: rgba(220, 53, 69, 0.1);
}

.alert-warning {
    border-left-color: var(--warning-color);
    background-color: rgba(255, 193, 7, 0.1);
}

.alert-info {
    border-left-color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.1);
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
}

/* Floating Action Button */
.fab-button {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    transition: all 0.2s ease-in-out;
}

.fab-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
    
    .stats-card h4 {
        font-size: 2rem;
    }
    
    .fab-button {
        bottom: 1rem;
        right: 1rem;
        width: 48px;
        height: 48px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #121212;
        color: #ffffff;
    }
    
    .card {
        background-color: #1e1e1e;
        color: #ffffff;
    }
    
    .form-control, .form-select {
        background-color: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }
    
    .form-control:focus, .form-select:focus {
        background-color: #2d2d2d;
        border-color: var(--primary-color);
        color: #ffffff;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-tiktok {
    color: var(--tiktok-color) !important;
}

.bg-tiktok {
    background-color: var(--tiktok-color) !important;
}

.border-tiktok {
    border-color: var(--tiktok-color) !important;
}

.cursor-pointer {
    cursor: pointer;
}

.user-select-none {
    user-select: none;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
