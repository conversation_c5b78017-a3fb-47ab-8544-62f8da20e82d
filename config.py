# config.py

import os
from typing import List, Tuple, Union

def get_env_var(key: str, default: str = "") -> str:
    """Get environment variable with fallback to default"""
    return os.getenv(key, default)

def get_env_int(key: str, default: int) -> int:
    """Get environment variable as integer with fallback"""
    try:
        return int(os.getenv(key, str(default)))
    except (ValueError, TypeError):
        return default

def parse_share_rules(rules_str: str) -> List[Tuple[int, Union[int, float], int]]:
    """Parse share rules from string format"""
    rules = []
    try:
        for rule in rules_str.split(','):
            parts = rule.strip().split(':')
            if len(parts) == 3:
                min_likes = int(parts[0])
                max_likes = int(parts[1]) if parts[1] != '999999999' else float("inf")
                shares = int(parts[2])
                rules.append((min_likes, max_likes, shares))
    except (ValueError, IndexError):
        # Fallback to default rules if parsing fails
        return [
            (0, 100, 0),
            (100, 1000, 50),
            (1000, 5000, 100),
            (5000, float("inf"), 150),
        ]
    return rules if rules else [
        (0, 100, 0),
        (100, 1000, 50),
        (1000, 5000, 100),
        (5000, float("inf"), 150),
    ]

# Telegram credentials
TELEGRAM_BOT_TOKEN = get_env_var("TELEGRAM_BOT_TOKEN", "**********************************************")
TELEGRAM_USER_ID = get_env_int("TELEGRAM_USER_ID", 1828333157)

# TikTok login (optional)
TIKTOK_USERNAME = get_env_var("TIKTOK_USERNAME", "sociaixzl3s")
TIKTOK_PASSWORD = get_env_var("TIKTOK_PASSWORD", "Virksaab@12345")

# Target usernames
TARGET_USERNAMES_STR = get_env_var("TARGET_USERNAMES", "hiamnoone")
TARGET_USERNAMES = [username.strip() for username in TARGET_USERNAMES_STR.split(',') if username.strip()]

# Proxy list path (local file)
PROXY_LIST_PATH = get_env_var("PROXY_LIST_PATH", "proxy.txt")

# Auto proxy settings
AUTO_PROXY_ENABLED = get_env_var("AUTO_PROXY_ENABLED", "true").lower() == "true"
PREMIUM_PROXY_API_KEY = get_env_var("PREMIUM_PROXY_API_KEY", "")

# Recheck interval (in hours)
RECHECK_INTERVAL_HOURS = get_env_int("RECHECK_INTERVAL_HOURS", 2)

# Share thresholds
SHARE_RULES_STR = get_env_var("SHARE_RULES", "0:100:0,100:1000:50,1000:5000:100,5000:999999999:150")
SHARE_RULES = parse_share_rules(SHARE_RULES_STR)

# Print configuration (without sensitive data)
if __name__ == "__main__":
    print("Configuration loaded:")
    print(f"Target usernames: {TARGET_USERNAMES}")
    print(f"Recheck interval: {RECHECK_INTERVAL_HOURS} hours")
    print(f"Share rules: {SHARE_RULES}")
    print(f"Proxy file: {PROXY_LIST_PATH}")
    print(f"Auto proxy enabled: {AUTO_PROXY_ENABLED}")
    print(f"Premium proxy API: {'Configured' if PREMIUM_PROXY_API_KEY else 'Not configured'}")
    print(f"Telegram configured: {'Yes' if TELEGRAM_BOT_TOKEN and TELEGRAM_USER_ID else 'No'}")