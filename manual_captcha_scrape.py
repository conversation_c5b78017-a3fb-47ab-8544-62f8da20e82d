#!/usr/bin/env python3
# manual_captcha_scrape.py

"""
TikTok scrape with manual CAPTCHA solving (browser visible)
"""

import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
from config import TARGET_USERNAMES
from telegram_logger import send_telegram_message
from utils import log_to_console, parse_like_count

def calculate_share_count_test(likes):
    """Test version of calculate_share_count with lower thresholds"""
    rules = [
        (0, 10, 1),      # 0-9 likes: 1 share
        (10, 50, 2),     # 10-49 likes: 2 shares  
        (50, 100, 5),    # 50-99 likes: 5 shares
        (100, 1000, 50), # 100-999 likes: 50 shares
        (1000, 5000, 100), # 1000-4999 likes: 100 shares
        (5000, float('inf'), 150) # 5000+ likes: 150 shares
    ]
    
    for min_likes, max_likes, shares in rules:
        if min_likes <= likes < max_likes:
            return shares
    return 0

async def manual_captcha_scrape():
    """Scrape with visible browser for manual CAPTCHA solving"""
    try:
        log_to_console("🌐 Starting manual CAPTCHA scrape (browser visible)")
        await send_telegram_message("🌐 Starting manual CAPTCHA scrape")

        async with async_playwright() as p:
            # Launch browser in non-headless mode
            browser = await p.chromium.launch(
                headless=False,  # Show browser for manual interaction
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--start-maximized'
                ]
            )
            
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            page = await context.new_page()
            
            try:
                for username in TARGET_USERNAMES:
                    url = f"https://www.tiktok.com/@{username}"
                    
                    log_to_console(f"🔍 Processing @{username}")
                    await send_telegram_message(f"🔍 Processing @{username}")
                    
                    print(f"\n{'='*60}")
                    print(f"🌐 Navigating to: {url}")
                    print(f"🤖 If CAPTCHA appears, please solve it manually in the browser")
                    print(f"⏳ The script will wait for you to solve it...")
                    print(f"{'='*60}")
                    
                    # Navigate to user page
                    try:
                        response = await page.goto(url, wait_until='load', timeout=60000)
                        log_to_console(f"✅ Loaded @{username} page (status: {response.status})")
                        
                        # Wait for user to solve CAPTCHA if needed
                        print("\n🔍 Checking for videos...")
                        print("💡 If you see CAPTCHA, solve it now!")
                        print("⏳ Waiting 30 seconds for page to load and CAPTCHA to be solved...")
                        
                        await asyncio.sleep(30)  # Give time for manual CAPTCHA solving
                        
                        # Try to find videos
                        video_selectors = [
                            "div[data-e2e='user-post-item'] a",
                            "a[href*='/video/']",
                            "[data-e2e*='post'] a",
                            "div[class*='video'] a"
                        ]
                        
                        videos = []
                        for selector in video_selectors:
                            videos = await page.query_selector_all(selector)
                            if videos:
                                log_to_console(f"✅ Found {len(videos)} videos with selector: {selector}")
                                break
                        
                        if not videos:
                            log_to_console(f"❌ No videos found for @{username}")
                            print(f"❌ No videos found. CAPTCHA might still be active.")
                            print(f"🔄 Please solve CAPTCHA and press Enter to continue...")
                            input("Press Enter when ready...")
                            
                            # Try again after manual intervention
                            for selector in video_selectors:
                                videos = await page.query_selector_all(selector)
                                if videos:
                                    log_to_console(f"✅ Found {len(videos)} videos after manual intervention")
                                    break
                        
                        if not videos:
                            await send_telegram_message(f"❌ Still no videos found for @{username}")
                            continue
                        
                        await send_telegram_message(f"📹 Found {len(videos)} videos for @{username}")
                        
                        # Collect video URLs
                        video_urls = []
                        for i, video in enumerate(videos[:3]):  # Process max 3 videos
                            try:
                                href = await video.get_attribute("href")
                                if href:
                                    if href.startswith("http"):
                                        video_url = href
                                    else:
                                        video_url = f"https://www.tiktok.com{href}"
                                    video_urls.append(video_url)
                            except Exception as e:
                                log_to_console(f"[ERROR] Error getting href for video {i+1}: {e}")
                                continue
                        
                        log_to_console(f"📋 Collected {len(video_urls)} video URLs")
                        
                        # Process videos
                        videos_processed = 0
                        videos_shared = 0
                        
                        for i, video_url in enumerate(video_urls):
                            try:
                                log_to_console(f"🎥 Processing video {i+1}: {video_url}")
                                print(f"\n🎥 Processing video {i+1}: {video_url}")
                                
                                # Navigate to video
                                await page.goto(video_url, wait_until='load', timeout=30000)
                                
                                print("⏳ Waiting 10 seconds for video page to load...")
                                await asyncio.sleep(10)
                                
                                videos_processed += 1
                                
                                # Try to get like count
                                like_selectors = [
                                    "strong[data-e2e='like-count']",
                                    "[data-e2e='like-count']",
                                    "strong:has-text('K')",
                                    "strong:has-text('M')"
                                ]
                                
                                likes = 0
                                for selector in like_selectors:
                                    try:
                                        like_element = await page.query_selector(selector)
                                        if like_element:
                                            like_text = await like_element.inner_text()
                                            likes = parse_like_count(like_text)
                                            log_to_console(f"👍 Video has {likes} likes")
                                            break
                                    except:
                                        continue
                                
                                if likes > 0:
                                    share_count = calculate_share_count_test(likes)
                                    if share_count > 0:
                                        log_to_console(f"📤 Video qualifies for {share_count} shares")
                                        videos_shared += 1
                                        await send_telegram_message(f"📢 Video {video_url} qualifies for {share_count} shares")
                                    else:
                                        log_to_console(f"[INFO] Video doesn't meet share criteria ({likes} likes)")
                                else:
                                    log_to_console(f"[WARNING] Could not find like count for {video_url}")
                                
                            except Exception as video_error:
                                log_to_console(f"[ERROR] Error processing video {i+1}: {video_error}")
                                continue
                        
                        log_to_console(f"✅ Completed @{username}: {videos_processed} processed, {videos_shared} qualified")
                        await send_telegram_message(f"✅ @{username}: {videos_processed} processed, {videos_shared} qualified")
                        
                    except PlaywrightTimeoutError:
                        log_to_console(f"[TIMEOUT] Could not load {url}")
                        await send_telegram_message(f"⏰ Timeout loading @{username}")
                    except Exception as e:
                        log_to_console(f"[ERROR] Exception for @{username}: {e}")
                        await send_telegram_message(f"❌ Error for @{username}: {str(e)[:100]}")
                
                print(f"\n{'='*60}")
                print(f"✅ Manual CAPTCHA scrape completed!")
                print(f"🌐 Browser will close in 10 seconds...")
                print(f"{'='*60}")
                
                await asyncio.sleep(10)
                
            finally:
                await browser.close()
                
        log_to_console("✅ Manual CAPTCHA scrape completed")
        await send_telegram_message("✅ Manual CAPTCHA scrape completed")
        
    except Exception as e:
        log_to_console(f"[ERROR] Manual CAPTCHA scrape failed: {e}")
        await send_telegram_message(f"❌ Manual CAPTCHA scrape failed: {str(e)[:100]}")

if __name__ == "__main__":
    print("🤖 TikTok Manual CAPTCHA Scraper")
    print("=" * 50)
    print("📋 This script will open a browser window")
    print("🤖 If CAPTCHA appears, solve it manually")
    print("⏳ The script will wait for you")
    print("=" * 50)
    
    input("Press Enter to start...")
    asyncio.run(manual_captcha_scrape())
