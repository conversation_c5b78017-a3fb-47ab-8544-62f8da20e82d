{% extends "base.html" %}

{% block title %}Logs - TikTok Bot Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-file-alt me-2"></i>Bot Logs</h2>
                <p class="text-muted">Real-time logs from your TikTok bot</p>
            </div>
            <div>
                <button class="btn btn-outline-primary me-2" onclick="refreshLogs()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
                <button class="btn btn-outline-secondary me-2" onclick="clearLogs()">
                    <i class="fas fa-trash me-1"></i>Clear
                </button>
                <button class="btn btn-outline-success" onclick="downloadLogs()">
                    <i class="fas fa-download me-1"></i>Download
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>Live Logs
                            <span id="log-count" class="badge bg-secondary ms-2">0</span>
                        </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end">
                            <!-- Log Level Filter -->
                            <select class="form-select form-select-sm me-2" id="log-level-filter" style="width: auto;">
                                <option value="all">All Levels</option>
                                <option value="info">Info</option>
                                <option value="warning">Warning</option>
                                <option value="error">Error</option>
                                <option value="success">Success</option>
                            </select>
                            
                            <!-- Auto-scroll Toggle -->
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="auto-scroll" checked>
                                <label class="form-check-label" for="auto-scroll">Auto-scroll</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="logs-container" style="height: 600px; overflow-y: auto; background-color: #1e1e1e; color: #ffffff; font-family: 'Courier New', monospace;">
                    <div class="p-3">
                        <div class="text-center text-muted">
                            <i class="fas fa-spinner fa-spin me-2"></i>Loading logs...
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <small class="text-muted">
                            Last updated: <span id="last-updated">Never</span>
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            Auto-refresh every 5 seconds
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Log Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Search Logs</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="search-term" class="form-label">Search Term</label>
                    <input type="text" class="form-control" id="search-term" placeholder="Enter search term...">
                </div>
                <div class="mb-3">
                    <label for="search-date" class="form-label">Date Range</label>
                    <input type="date" class="form-control" id="search-date">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="searchLogs()">Search</button>
            </div>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<div class="position-fixed bottom-0 end-0 p-3">
    <button class="btn btn-primary rounded-circle" style="width: 56px; height: 56px;" 
            data-bs-toggle="modal" data-bs-target="#searchModal">
        <i class="fas fa-search"></i>
    </button>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let logsData = [];
let autoRefreshInterval;
let lastLogCount = 0;

$(document).ready(function() {
    loadLogs();
    startAutoRefresh();
    
    // Log level filter
    $('#log-level-filter').change(function() {
        filterLogs();
    });
    
    // Auto-scroll toggle
    $('#auto-scroll').change(function() {
        if (this.checked) {
            scrollToBottom();
        }
    });
});

function loadLogs() {
    $.get('/api/logs', function(data) {
        logsData = data.logs || [];
        displayLogs();
        updateLogCount();
        updateLastUpdated();
    }).fail(function() {
        showError('Failed to load logs');
    });
}

function displayLogs() {
    const container = $('#logs-container');
    const level = $('#log-level-filter').val();
    
    let filteredLogs = logsData;
    if (level !== 'all') {
        filteredLogs = logsData.filter(log => log.type === level);
    }
    
    if (filteredLogs.length === 0) {
        container.html('<div class="p-3 text-center text-muted">No logs available</div>');
        return;
    }
    
    let html = '<div class="p-2">';
    filteredLogs.forEach(function(log) {
        const logClass = getLogClass(log.type);
        const icon = getLogIcon(log.type);
        
        html += `
            <div class="log-entry mb-1 p-2 rounded" style="border-left: 3px solid ${getLogColor(log.type)};">
                <span class="text-muted">[${log.timestamp}]</span>
                <i class="${icon} me-1" style="color: ${getLogColor(log.type)};"></i>
                <span class="${logClass}">${escapeHtml(log.message)}</span>
            </div>
        `;
    });
    html += '</div>';
    
    container.html(html);
    
    if ($('#auto-scroll').is(':checked')) {
        scrollToBottom();
    }
}

function getLogClass(type) {
    switch(type) {
        case 'error': return 'text-danger';
        case 'warning': return 'text-warning';
        case 'success': return 'text-success';
        case 'info': 
        default: return 'text-light';
    }
}

function getLogIcon(type) {
    switch(type) {
        case 'error': return 'fas fa-exclamation-circle';
        case 'warning': return 'fas fa-exclamation-triangle';
        case 'success': return 'fas fa-check-circle';
        case 'info':
        default: return 'fas fa-info-circle';
    }
}

function getLogColor(type) {
    switch(type) {
        case 'error': return '#dc3545';
        case 'warning': return '#ffc107';
        case 'success': return '#28a745';
        case 'info':
        default: return '#17a2b8';
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function scrollToBottom() {
    const container = document.getElementById('logs-container');
    container.scrollTop = container.scrollHeight;
}

function updateLogCount() {
    $('#log-count').text(logsData.length);
    
    // Highlight if new logs
    if (logsData.length > lastLogCount) {
        $('#log-count').addClass('bg-warning text-dark').removeClass('bg-secondary');
        setTimeout(function() {
            $('#log-count').removeClass('bg-warning text-dark').addClass('bg-secondary');
        }, 2000);
    }
    lastLogCount = logsData.length;
}

function updateLastUpdated() {
    const now = new Date();
    $('#last-updated').text(now.toLocaleTimeString());
}

function startAutoRefresh() {
    autoRefreshInterval = setInterval(function() {
        loadLogs();
    }, 5000);
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
}

function refreshLogs() {
    const button = $('button[onclick="refreshLogs()"]');
    const originalHtml = button.html();
    
    button.html('<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...');
    
    loadLogs();
    
    setTimeout(function() {
        button.html(originalHtml);
    }, 1000);
}

function clearLogs() {
    if (confirm('Are you sure you want to clear all logs?')) {
        // This would need backend implementation
        showAlert('warning', 'Clear logs functionality needs backend implementation');
    }
}

function downloadLogs() {
    if (logsData.length === 0) {
        showAlert('warning', 'No logs to download');
        return;
    }
    
    let content = 'TikTok Bot Logs\n';
    content += '================\n\n';
    
    logsData.forEach(function(log) {
        content += `[${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}\n`;
    });
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tiktok-bot-logs-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    showAlert('success', 'Logs downloaded successfully');
}

function filterLogs() {
    displayLogs();
}

function searchLogs() {
    const searchTerm = $('#search-term').val().toLowerCase();
    const searchDate = $('#search-date').val();
    
    if (!searchTerm && !searchDate) {
        showAlert('warning', 'Please enter a search term or select a date');
        return;
    }
    
    let filteredLogs = logsData;
    
    if (searchTerm) {
        filteredLogs = filteredLogs.filter(log => 
            log.message.toLowerCase().includes(searchTerm)
        );
    }
    
    if (searchDate) {
        filteredLogs = filteredLogs.filter(log => 
            log.timestamp.startsWith(searchDate)
        );
    }
    
    // Temporarily display filtered results
    const container = $('#logs-container');
    let html = '<div class="p-2">';
    
    if (filteredLogs.length === 0) {
        html += '<div class="text-center text-muted">No logs found matching your search</div>';
    } else {
        filteredLogs.forEach(function(log) {
            const logClass = getLogClass(log.type);
            const icon = getLogIcon(log.type);
            
            html += `
                <div class="log-entry mb-1 p-2 rounded" style="border-left: 3px solid ${getLogColor(log.type)};">
                    <span class="text-muted">[${log.timestamp}]</span>
                    <i class="${icon} me-1" style="color: ${getLogColor(log.type)};"></i>
                    <span class="${logClass}">${escapeHtml(log.message)}</span>
                </div>
            `;
        });
    }
    
    html += '</div>';
    container.html(html);
    
    $('#searchModal').modal('hide');
    showAlert('info', `Found ${filteredLogs.length} matching logs`);
}

function showError(message) {
    const container = $('#logs-container');
    container.html(`
        <div class="p-3 text-center">
            <i class="fas fa-exclamation-triangle text-warning fa-2x mb-2"></i>
            <div class="text-danger">${message}</div>
            <button class="btn btn-outline-light btn-sm mt-2" onclick="loadLogs()">
                <i class="fas fa-retry me-1"></i>Retry
            </button>
        </div>
    `);
}

function showAlert(type, message) {
    const alert = $('<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                   message +
                   '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                   '</div>');
    
    $('.container-fluid').prepend(alert);
    
    setTimeout(function() {
        alert.alert('close');
    }, 5000);
}

// Cleanup on page unload
$(window).on('beforeunload', function() {
    stopAutoRefresh();
});
</script>
{% endblock %}
