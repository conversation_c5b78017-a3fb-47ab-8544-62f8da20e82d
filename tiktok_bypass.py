#!/usr/bin/env python3
"""
TikTok Bypass Methods
Các phương pháp để bypass TikTok login và CAPTCHA
"""

import asyncio
import random
import time
from playwright.async_api import async_playwright

class TikTokBypass:
    def __init__(self):
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
        self.mobile_user_agents = [
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHT<PERSON>, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Android 13; Mobile; rv:109.0) Gecko/109.0 Firefox/109.0"
        ]
    
    async def create_stealth_browser(self, proxy=None, mobile=False):
        """Create a stealth browser instance"""
        async with async_playwright() as p:
            # Choose user agent
            user_agent = random.choice(self.mobile_user_agents if mobile else self.user_agents)
            
            # Browser args for stealth
            args = [
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-dev-shm-usage',
                '--no-first-run',
                '--disable-extensions',
                '--disable-default-apps',
                f'--user-agent={user_agent}'
            ]
            
            # Launch browser
            browser = await p.chromium.launch(
                headless=True,
                proxy={"server": proxy} if proxy else None,
                args=args
            )
            
            # Create context with additional stealth
            context = await browser.new_context(
                user_agent=user_agent,
                viewport={'width': 375, 'height': 667} if mobile else {'width': 1920, 'height': 1080},
                device_scale_factor=2 if mobile else 1
            )
            
            # Add stealth scripts
            await context.add_init_script("""
                // Remove webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // Mock languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
                
                // Mock plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
            """)
            
            page = await context.new_page()
            
            # Set additional headers
            await page.set_extra_http_headers({
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            })
            
            return browser, page
    
    async def bypass_login_popup(self, page):
        """Try multiple methods to bypass login popup"""
        methods = [
            self._method_close_button,
            self._method_escape_key,
            self._method_click_outside,
            self._method_reload_page,
            self._method_mobile_redirect
        ]
        
        for i, method in enumerate(methods, 1):
            print(f"🔄 Trying bypass method {i}/{len(methods)}: {method.__name__}")
            
            try:
                success = await method(page)
                if success:
                    print(f"✅ Bypass successful with method {i}")
                    return True
                else:
                    print(f"❌ Method {i} failed")
            except Exception as e:
                print(f"❌ Method {i} error: {e}")
            
            # Wait between attempts
            await page.wait_for_timeout(2000)
        
        print("❌ All bypass methods failed")
        return False
    
    async def _method_close_button(self, page):
        """Method 1: Find and click close button"""
        close_selectors = [
            "button[aria-label*='Close']",
            "button[data-e2e='close-button']", 
            "svg[data-e2e='close-icon']",
            "button:has-text('×')",
            ".close-button",
            "[data-testid='close-button']"
        ]
        
        for selector in close_selectors:
            try:
                element = await page.wait_for_selector(selector, timeout=3000)
                if element:
                    await element.click()
                    await page.wait_for_timeout(2000)
                    return await self._check_popup_gone(page)
            except:
                continue
        
        return False
    
    async def _method_escape_key(self, page):
        """Method 2: Press Escape key"""
        await page.keyboard.press('Escape')
        await page.wait_for_timeout(2000)
        return await self._check_popup_gone(page)
    
    async def _method_click_outside(self, page):
        """Method 3: Click outside modal"""
        await page.click('body', position={'x': 50, 'y': 50})
        await page.wait_for_timeout(2000)
        return await self._check_popup_gone(page)
    
    async def _method_reload_page(self, page):
        """Method 4: Reload page"""
        await page.reload()
        await page.wait_for_timeout(5000)
        return await self._check_popup_gone(page)
    
    async def _method_mobile_redirect(self, page):
        """Method 5: Try mobile version"""
        current_url = page.url
        if 'm.tiktok.com' not in current_url:
            mobile_url = current_url.replace('www.tiktok.com', 'm.tiktok.com')
            await page.goto(mobile_url)
            await page.wait_for_timeout(5000)
            return await self._check_popup_gone(page)
        return False
    
    async def _check_popup_gone(self, page):
        """Check if login popup is gone"""
        popup_selectors = [
            "text=Log in to TikTok",
            "text=Use QR code",
            "div[role='dialog']",
            "div[data-e2e='login-modal']"
        ]
        
        for selector in popup_selectors:
            try:
                element = await page.wait_for_selector(selector, timeout=1000)
                if element:
                    return False  # Popup still there
            except:
                continue
        
        return True  # No popup found
    
    async def get_videos_alternative_methods(self, page, username):
        """Try alternative methods to get videos"""
        methods = [
            self._get_videos_standard,
            self._get_videos_mobile,
            self._get_videos_api_like,
            self._get_videos_scroll_method
        ]
        
        for i, method in enumerate(methods, 1):
            print(f"🎥 Trying video extraction method {i}: {method.__name__}")
            
            try:
                videos = await method(page, username)
                if videos:
                    print(f"✅ Found {len(videos)} videos with method {i}")
                    return videos
            except Exception as e:
                print(f"❌ Method {i} error: {e}")
        
        return []
    
    async def _get_videos_standard(self, page, username):
        """Standard method"""
        await page.wait_for_selector("div[data-e2e='user-post-item']", timeout=10000)
        videos = await page.query_selector_all("div[data-e2e='user-post-item'] a")
        return [await v.get_attribute("href") for v in videos]
    
    async def _get_videos_mobile(self, page, username):
        """Mobile method"""
        mobile_selectors = [
            "div[data-e2e='user-post-item-list'] a",
            ".video-feed-item a",
            "a[href*='/video/']"
        ]
        
        for selector in mobile_selectors:
            try:
                await page.wait_for_selector(selector, timeout=5000)
                videos = await page.query_selector_all(selector)
                if videos:
                    return [await v.get_attribute("href") for v in videos]
            except:
                continue
        
        return []
    
    async def _get_videos_api_like(self, page, username):
        """API-like method using network requests"""
        # This would require intercepting network requests
        # For now, return empty
        return []
    
    async def _get_videos_scroll_method(self, page, username):
        """Scroll to load more videos"""
        videos = []
        
        # Scroll down multiple times to load videos
        for i in range(5):
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(2000)
            
            # Try to find videos
            try:
                video_elements = await page.query_selector_all("a[href*='/video/']")
                current_videos = [await v.get_attribute("href") for v in video_elements]
                videos.extend(current_videos)
            except:
                continue
        
        # Remove duplicates
        return list(set(videos))

async def test_bypass_methods():
    """Test bypass methods"""
    bypass = TikTokBypass()
    
    test_users = ["its.sahiba2233", "iamvirk"]
    
    for username in test_users:
        print(f"\n🧪 Testing bypass for @{username}")
        print("=" * 40)
        
        try:
            browser, page = await bypass.create_stealth_browser()
            
            url = f"https://www.tiktok.com/@{username}"
            await page.goto(url, timeout=30000)
            await page.wait_for_timeout(3000)
            
            # Check for login popup
            popup_present = not await bypass._check_popup_gone(page)
            
            if popup_present:
                print("🔐 Login popup detected, attempting bypass...")
                success = await bypass.bypass_login_popup(page)
                
                if success:
                    print("✅ Bypass successful!")
                    
                    # Try to get videos
                    videos = await bypass.get_videos_alternative_methods(page, username)
                    print(f"🎥 Found {len(videos)} videos")
                    
                    if videos:
                        for i, video in enumerate(videos[:3], 1):
                            print(f"   {i}. {video}")
                else:
                    print("❌ Bypass failed")
            else:
                print("✅ No login popup, proceeding...")
                videos = await bypass.get_videos_alternative_methods(page, username)
                print(f"🎥 Found {len(videos)} videos")
            
            await browser.close()
            
        except Exception as e:
            print(f"❌ Error testing @{username}: {e}")

if __name__ == "__main__":
    asyncio.run(test_bypass_methods())
