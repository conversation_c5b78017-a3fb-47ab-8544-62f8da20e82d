import os
import sys
import json
import asyncio
import threading
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_cors import CORS
import subprocess
import signal

# Import bot modules
from config import (
    TELEGRAM_BOT_TOKEN, TELEGRAM_USER_ID, TARGET_USERNAMES,
    TIKTOK_USERNAME, TIKTOK_PASSWORD, SHARE_RULES, RECHECK_INTERVAL_HOURS
)
from utils import load_previous_data, save_video_data, log_to_console
from telegram_logger import send_telegram_message

app = Flask(__name__)
app.secret_key = 'tiktok_bot_secret_key_change_this'
CORS(app)

# Global variables for bot control
bot_process = None
bot_status = "stopped"
bot_logs = []
max_logs = 1000

class BotManager:
    def __init__(self):
        self.process = None
        self.status = "stopped"
        self.start_time = None
        
    def start_bot(self):
        """Start the bot in a separate process"""
        if self.process and self.process.poll() is None:
            return False, "Bot is already running"
        
        try:
            self.process = subprocess.Popen(
                [sys.executable, 'simple_bot.py'],  # Use simple_bot for testing
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=os.getcwd()
            )
            self.status = "running"
            self.start_time = datetime.now()
            
            # Start log monitoring in background
            threading.Thread(target=self._monitor_logs, daemon=True).start()
            
            return True, "Bot started successfully"
        except Exception as e:
            return False, f"Failed to start bot: {str(e)}"
    
    def stop_bot(self):
        """Stop the bot"""
        if not self.process or self.process.poll() is not None:
            return False, "Bot is not running"
        
        try:
            self.process.terminate()
            self.process.wait(timeout=10)
            self.status = "stopped"
            self.start_time = None
            return True, "Bot stopped successfully"
        except subprocess.TimeoutExpired:
            self.process.kill()
            self.status = "stopped"
            self.start_time = None
            return True, "Bot force stopped"
        except Exception as e:
            return False, f"Failed to stop bot: {str(e)}"
    
    def get_status(self):
        """Get bot status"""
        if self.process and self.process.poll() is None:
            self.status = "running"
        else:
            self.status = "stopped"
            
        return {
            "status": self.status,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "uptime": str(datetime.now() - self.start_time) if self.start_time else None
        }
    
    def _monitor_logs(self):
        """Monitor bot logs in background"""
        global bot_logs
        
        if not self.process:
            return
            
        try:
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    log_entry = {
                        "timestamp": timestamp,
                        "message": line.strip(),
                        "type": "info"
                    }
                    bot_logs.append(log_entry)
                    
                    # Keep only last max_logs entries
                    if len(bot_logs) > max_logs:
                        bot_logs = bot_logs[-max_logs:]
                        
                if self.process.poll() is not None:
                    break
        except Exception as e:
            log_entry = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "message": f"Log monitoring error: {str(e)}",
                "type": "error"
            }
            bot_logs.append(log_entry)

# Initialize bot manager
bot_manager = BotManager()

@app.route('/')
def dashboard():
    """Main dashboard"""
    status = bot_manager.get_status()
    video_data = load_previous_data()
    
    stats = {
        "total_videos": len(video_data),
        "total_shares": sum(v.get("shared_count", 0) for v in video_data.values()),
        "target_users": len(TARGET_USERNAMES),
        "share_rules": len(SHARE_RULES)
    }
    
    return render_template('dashboard.html', 
                         status=status, 
                         stats=stats,
                         target_users=TARGET_USERNAMES)

@app.route('/settings')
def settings():
    """Settings page"""
    # Convert float('inf') to a large number for template display
    display_share_rules = []
    for rule in SHARE_RULES:
        min_likes, max_likes, shares = rule
        # Convert float('inf') to a large number for display
        if isinstance(max_likes, float) and max_likes == float('inf'):
            max_likes = 999999999
        display_share_rules.append((min_likes, max_likes, shares))

    current_settings = {
        "telegram_token": TELEGRAM_BOT_TOKEN,
        "telegram_user_id": TELEGRAM_USER_ID,
        "tiktok_username": TIKTOK_USERNAME,
        "tiktok_password": TIKTOK_PASSWORD,
        "target_usernames": TARGET_USERNAMES,
        "recheck_interval": RECHECK_INTERVAL_HOURS,
        "share_rules": display_share_rules
    }
    return render_template('settings.html', settings=current_settings)

@app.route('/logs')
def logs():
    """Logs page"""
    return render_template('logs.html', logs=bot_logs)

@app.route('/videos')
def videos():
    """Videos history page"""
    video_data = load_previous_data()
    return render_template('videos.html', videos=video_data)

@app.route('/proxy-manager')
def proxy_manager():
    """Proxy management page"""
    return render_template('proxy_manager.html')

# API Routes
@app.route('/api/bot/start', methods=['POST'])
def api_start_bot():
    """API to start bot"""
    success, message = bot_manager.start_bot()
    return jsonify({"success": success, "message": message})

@app.route('/api/bot/stop', methods=['POST'])
def api_stop_bot():
    """API to stop bot"""
    success, message = bot_manager.stop_bot()
    return jsonify({"success": success, "message": message})

@app.route('/api/bot/start-manual-captcha', methods=['POST'])
def api_start_manual_captcha():
    """API to start bot with manual CAPTCHA solving"""
    try:
        # Stop current bot if running
        bot_manager.stop_bot()

        # Start manual CAPTCHA bot
        process = subprocess.Popen(
            [sys.executable, 'manual_captcha_scrape.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )

        bot_manager.process = process
        bot_manager.status = "running_manual_captcha"
        bot_manager.start_time = datetime.now()

        return jsonify({
            "success": True,
            "message": "Manual CAPTCHA bot started successfully. Browser window will open."
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Failed to start manual CAPTCHA bot: {str(e)}"
        })

@app.route('/api/bot/start-bypass-captcha', methods=['POST'])
def api_start_bypass_captcha():
    """API to start bot with automatic CAPTCHA bypass"""
    try:
        # Stop current bot if running
        bot_manager.stop_bot()

        # Start bypass CAPTCHA bot
        process = subprocess.Popen(
            [sys.executable, 'bypass_captcha_scrape.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )

        bot_manager.process = process
        bot_manager.status = "running_bypass_captcha"
        bot_manager.start_time = datetime.now()

        return jsonify({
            "success": True,
            "message": "Bypass CAPTCHA bot started successfully."
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Failed to start bypass CAPTCHA bot: {str(e)}"
        })

@app.route('/api/bot/status')
def api_bot_status():
    """API to get bot status"""
    return jsonify(bot_manager.get_status())

@app.route('/api/logs')
def api_logs():
    """API to get logs"""
    return jsonify({"logs": bot_logs[-100:]})  # Return last 100 logs

@app.route('/api/stats')
def api_stats():
    """API to get statistics"""
    video_data = load_previous_data()

    # Get proxy status
    proxy_status = get_proxy_status()

    # Get TikTok access status
    tiktok_status = get_tiktok_access_status()

    stats = {
        "total_videos": len(video_data),
        "total_shares": sum(v.get("shared_count", 0) for v in video_data.values()),
        "target_users": len(TARGET_USERNAMES),
        "recent_videos": list(video_data.items())[-10:],  # Last 10 videos
        "proxy_status": proxy_status,
        "tiktok_access": tiktok_status,
        "bot_health": get_bot_health()
    }
    return jsonify(stats)

@app.route('/api/settings', methods=['GET'])
def api_get_settings():
    """API to get current settings"""
    settings = {
        "telegram_token": TELEGRAM_BOT_TOKEN,
        "telegram_user_id": TELEGRAM_USER_ID,
        "tiktok_username": TIKTOK_USERNAME,
        "tiktok_password": TIKTOK_PASSWORD,
        "target_usernames": TARGET_USERNAMES,
        "recheck_interval": RECHECK_INTERVAL_HOURS,
        "share_rules": SHARE_RULES
    }
    return jsonify(settings)

@app.route('/api/settings', methods=['POST'])
def api_save_settings():
    """API to save settings"""
    try:
        data = request.get_json()

        # Update .env file
        env_updates = {}

        if 'telegram_token' in data:
            env_updates['TELEGRAM_BOT_TOKEN'] = data['telegram_token']
        if 'telegram_user_id' in data:
            env_updates['TELEGRAM_USER_ID'] = str(data['telegram_user_id'])
        if 'tiktok_username' in data:
            env_updates['TIKTOK_USERNAME'] = data['tiktok_username']
        if 'tiktok_password' in data:
            env_updates['TIKTOK_PASSWORD'] = data['tiktok_password']
        if 'target_usernames' in data:
            env_updates['TARGET_USERNAMES'] = ','.join(data['target_usernames'])
        if 'recheck_interval' in data:
            env_updates['RECHECK_INTERVAL_HOURS'] = str(data['recheck_interval'])
        if 'share_rules' in data:
            rules_str = ','.join([f"{r[0]}:{r[1]}:{r[2]}" for r in data['share_rules']])
            env_updates['SHARE_RULES'] = rules_str

        # Update .env file
        update_env_file(env_updates)

        return jsonify({"success": True, "message": "Settings saved successfully"})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/test-telegram', methods=['POST'])
def api_test_telegram():
    """API to test Telegram connection"""
    try:
        asyncio.run(send_telegram_message("🧪 Test message from TikTok Bot Web UI"))
        return jsonify({"success": True, "message": "Test message sent successfully"})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/settings/target-users', methods=['POST'])
def api_save_target_users():
    """API to save target users"""
    try:
        data = request.get_json()
        usernames = data.get('target_usernames', [])

        # Validate usernames
        if not isinstance(usernames, list):
            return jsonify({"success": False, "message": "Invalid usernames format"}), 400

        # Clean usernames
        clean_usernames = []
        for username in usernames:
            username = str(username).strip().replace('@', '')
            if username and len(username) > 0:
                clean_usernames.append(username)

        # Update .env file
        env_updates = {
            'TARGET_USERNAMES': ','.join(clean_usernames)
        }
        update_env_file(env_updates)

        # Update global variable (for immediate effect)
        global TARGET_USERNAMES
        TARGET_USERNAMES = clean_usernames

        return jsonify({
            "success": True,
            "message": f"Target users updated successfully! ({len(clean_usernames)} users)",
            "usernames": clean_usernames
        })
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/settings/tiktok', methods=['POST'])
def api_save_tiktok():
    """API to save TikTok account settings"""
    try:
        data = request.get_json()
        username = data.get('tiktok_username', '').strip()
        password = data.get('tiktok_password', '').strip()

        # Update .env file
        env_updates = {
            'TIKTOK_USERNAME': username,
            'TIKTOK_PASSWORD': password
        }
        update_env_file(env_updates)

        return jsonify({
            "success": True,
            "message": "TikTok account settings saved successfully!"
        })
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/settings/telegram', methods=['POST'])
def api_save_telegram():
    """API to save Telegram settings"""
    try:
        data = request.get_json()
        token = data.get('telegram_token', '').strip()
        user_id = data.get('telegram_user_id', 0)

        # Validate
        if token and not token.startswith(('1', '2', '3', '4', '5', '6', '7', '8', '9')):
            return jsonify({"success": False, "message": "Invalid Telegram bot token format"}), 400

        if user_id and (user_id < 1 or user_id > **********):
            return jsonify({"success": False, "message": "Invalid Telegram user ID"}), 400

        # Update .env file
        env_updates = {
            'TELEGRAM_BOT_TOKEN': token,
            'TELEGRAM_USER_ID': str(user_id)
        }
        update_env_file(env_updates)

        return jsonify({
            "success": True,
            "message": "Telegram settings saved successfully!"
        })
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/settings/bot', methods=['POST'])
def api_save_bot_settings():
    """API to save bot settings"""
    try:
        data = request.get_json()
        interval = data.get('recheck_interval', 2)

        # Validate
        if not isinstance(interval, int) or interval < 1 or interval > 24:
            return jsonify({"success": False, "message": "Recheck interval must be between 1-24 hours"}), 400

        # Update .env file
        env_updates = {
            'RECHECK_INTERVAL_HOURS': str(interval)
        }
        update_env_file(env_updates)

        return jsonify({
            "success": True,
            "message": f"Bot settings saved! Recheck interval: {interval} hours"
        })
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/test-tiktok-access', methods=['POST'])
def api_test_tiktok_access():
    """API to test TikTok access"""
    try:
        data = request.get_json()
        username = data.get('username', 'its.sahiba2233')

        # Run TikTok access test
        import subprocess
        result = subprocess.run(
            ['python', 'test_tiktok_access.py'],
            capture_output=True,
            text=True,
            timeout=60
        )

        return jsonify({
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr if result.stderr else None,
            "message": "TikTok access test completed"
        })
    except subprocess.TimeoutExpired:
        return jsonify({"success": False, "message": "Test timed out"}), 500
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/proxy/status')
def api_proxy_status():
    """API to get proxy status"""
    return jsonify(get_proxy_status())

@app.route('/api/proxy/test', methods=['POST'])
def api_test_proxy():
    """API to test proxy"""
    try:
        data = request.get_json()
        proxy = data.get('proxy', '')

        if not proxy:
            return jsonify({"success": False, "message": "No proxy provided"}), 400

        # Test proxy with a simple request
        import requests
        proxies = {
            'http': proxy,
            'https': proxy
        }

        response = requests.get(
            'https://httpbin.org/ip',
            proxies=proxies,
            timeout=10
        )

        if response.status_code == 200:
            ip_info = response.json()
            return jsonify({
                "success": True,
                "message": "Proxy is working",
                "ip": ip_info.get('origin', 'Unknown'),
                "proxy": proxy
            })
        else:
            return jsonify({
                "success": False,
                "message": f"Proxy test failed with status {response.status_code}"
            }), 400

    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/proxy/add', methods=['POST'])
def api_add_proxy():
    """API to add proxy"""
    try:
        data = request.get_json()
        proxy = data.get('proxy', '').strip()

        if not proxy:
            return jsonify({"success": False, "message": "No proxy provided"}), 400

        # Validate proxy format
        if not ('://' in proxy or ':' in proxy):
            return jsonify({"success": False, "message": "Invalid proxy format"}), 400

        # Add to proxy.txt
        with open('proxy.txt', 'a') as f:
            f.write(f"{proxy}\n")

        return jsonify({
            "success": True,
            "message": f"Proxy added: {proxy}"
        })
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

def get_proxy_status():
    """Get proxy status"""
    try:
        if os.path.exists('proxy.txt'):
            with open('proxy.txt', 'r') as f:
                proxies = [line.strip() for line in f if line.strip()]
            return {
                "total_proxies": len(proxies),
                "status": "available" if proxies else "empty",
                "current_proxy": proxies[0] if proxies else None
            }
        else:
            return {
                "total_proxies": 0,
                "status": "no_file",
                "current_proxy": None
            }
    except Exception as e:
        return {
            "total_proxies": 0,
            "status": "error",
            "error": str(e),
            "current_proxy": None
        }

def get_tiktok_access_status():
    """Get TikTok access status"""
    try:
        from proxy_handler import get_auto_proxy
        import requests

        # Get a working proxy
        proxy = get_auto_proxy()

        if not proxy:
            return {
                "last_check": datetime.now().isoformat(),
                "status": "error",
                "message": "No working proxy found",
                "login_required": False,
                "captcha_detected": False,
                "accessible_users": 0,
                "blocked_users": 0
            }

        # Test TikTok access with proxy
        proxies = {
            'http': f'http://{proxy}',
            'https': f'http://{proxy}'
        }

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # Test with a simple TikTok page
        test_url = "https://www.tiktok.com/"

        response = requests.get(
            test_url,
            proxies=proxies,
            headers=headers,
            timeout=15,
            allow_redirects=True
        )

        if response.status_code == 200:
            # Check if we got a proper TikTok page
            if "tiktok" in response.text.lower():
                return {
                    "last_check": datetime.now().isoformat(),
                    "status": "accessible",
                    "message": f"TikTok accessible via proxy {proxy}",
                    "proxy_used": proxy,
                    "login_required": "login" in response.text.lower(),
                    "captcha_detected": "captcha" in response.text.lower(),
                    "accessible_users": len(TARGET_USERNAMES),
                    "blocked_users": 0
                }
            else:
                return {
                    "last_check": datetime.now().isoformat(),
                    "status": "blocked",
                    "message": "TikTok may be blocked or redirected",
                    "proxy_used": proxy,
                    "login_required": False,
                    "captcha_detected": False,
                    "accessible_users": 0,
                    "blocked_users": len(TARGET_USERNAMES)
                }
        else:
            return {
                "last_check": datetime.now().isoformat(),
                "status": "error",
                "message": f"HTTP {response.status_code} - {response.reason}",
                "proxy_used": proxy,
                "login_required": False,
                "captcha_detected": False,
                "accessible_users": 0,
                "blocked_users": len(TARGET_USERNAMES)
            }

    except Exception as e:
        return {
            "last_check": datetime.now().isoformat(),
            "status": "error",
            "message": f"Network error: {str(e)}",
            "login_required": False,
            "captcha_detected": False,
            "accessible_users": 0,
            "blocked_users": len(TARGET_USERNAMES)
        }

def get_bot_health():
    """Get overall bot health status"""
    try:
        # Check if required files exist
        required_files = ['config.py', 'utils.py', 'scrape.py', 'telegram_logger.py']
        missing_files = [f for f in required_files if not os.path.exists(f)]

        # Check .env file
        env_ok = os.path.exists('.env')

        # Check if bot is running
        bot_running = bot_manager.status == "running"

        health_score = 100
        issues = []

        if missing_files:
            health_score -= 30
            issues.append(f"Missing files: {', '.join(missing_files)}")

        if not env_ok:
            health_score -= 20
            issues.append("Missing .env configuration")

        if not bot_running:
            health_score -= 10
            issues.append("Bot is not running")

        # Check recent errors in logs
        recent_errors = [log for log in bot_logs[-50:] if log.get('type') == 'error']
        if len(recent_errors) > 5:
            health_score -= 20
            issues.append(f"High error rate: {len(recent_errors)} errors in recent logs")

        status = "excellent" if health_score >= 90 else \
                "good" if health_score >= 70 else \
                "fair" if health_score >= 50 else "poor"

        return {
            "score": health_score,
            "status": status,
            "issues": issues,
            "last_check": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "score": 0,
            "status": "error",
            "issues": [f"Health check failed: {str(e)}"],
            "last_check": datetime.now().isoformat()
        }

def update_env_file(updates):
    """Update .env file with new values"""
    env_file = '.env'

    # Read existing .env file
    env_vars = {}
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value

    # Update with new values
    env_vars.update(updates)

    # Write back to .env file
    with open(env_file, 'w') as f:
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")

if __name__ == '__main__':
    print("🚀 Starting TikTok Bot Web UI...")
    print("📱 Access at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
