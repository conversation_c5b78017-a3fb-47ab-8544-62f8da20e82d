import os
import json
import asyncio
import threading
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_cors import CORS
import subprocess
import signal

# Import bot modules
from config import (
    TELEGRAM_BOT_TOKEN, TELEGRAM_USER_ID, TARGET_USERNAMES,
    TIKTOK_USERNAME, TIKTOK_PASSWORD, SHARE_RULES, RECHECK_INTERVAL_HOURS
)
from utils import load_previous_data, save_video_data, log_to_console
from telegram_logger import send_telegram_message

app = Flask(__name__)
app.secret_key = 'tiktok_bot_secret_key_change_this'
CORS(app)

# Global variables for bot control
bot_process = None
bot_status = "stopped"
bot_logs = []
max_logs = 1000

class BotManager:
    def __init__(self):
        self.process = None
        self.status = "stopped"
        self.start_time = None
        
    def start_bot(self):
        """Start the bot in a separate process"""
        if self.process and self.process.poll() is None:
            return False, "Bot is already running"
        
        try:
            self.process = subprocess.Popen(
                ['python', 'index.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            self.status = "running"
            self.start_time = datetime.now()
            
            # Start log monitoring in background
            threading.Thread(target=self._monitor_logs, daemon=True).start()
            
            return True, "Bot started successfully"
        except Exception as e:
            return False, f"Failed to start bot: {str(e)}"
    
    def stop_bot(self):
        """Stop the bot"""
        if not self.process or self.process.poll() is not None:
            return False, "Bot is not running"
        
        try:
            self.process.terminate()
            self.process.wait(timeout=10)
            self.status = "stopped"
            self.start_time = None
            return True, "Bot stopped successfully"
        except subprocess.TimeoutExpired:
            self.process.kill()
            self.status = "stopped"
            self.start_time = None
            return True, "Bot force stopped"
        except Exception as e:
            return False, f"Failed to stop bot: {str(e)}"
    
    def get_status(self):
        """Get bot status"""
        if self.process and self.process.poll() is None:
            self.status = "running"
        else:
            self.status = "stopped"
            
        return {
            "status": self.status,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "uptime": str(datetime.now() - self.start_time) if self.start_time else None
        }
    
    def _monitor_logs(self):
        """Monitor bot logs in background"""
        global bot_logs
        
        if not self.process:
            return
            
        try:
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    log_entry = {
                        "timestamp": timestamp,
                        "message": line.strip(),
                        "type": "info"
                    }
                    bot_logs.append(log_entry)
                    
                    # Keep only last max_logs entries
                    if len(bot_logs) > max_logs:
                        bot_logs = bot_logs[-max_logs:]
                        
                if self.process.poll() is not None:
                    break
        except Exception as e:
            log_entry = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "message": f"Log monitoring error: {str(e)}",
                "type": "error"
            }
            bot_logs.append(log_entry)

# Initialize bot manager
bot_manager = BotManager()

@app.route('/')
def dashboard():
    """Main dashboard"""
    status = bot_manager.get_status()
    video_data = load_previous_data()
    
    stats = {
        "total_videos": len(video_data),
        "total_shares": sum(v.get("shared_count", 0) for v in video_data.values()),
        "target_users": len(TARGET_USERNAMES),
        "share_rules": len(SHARE_RULES)
    }
    
    return render_template('dashboard.html', 
                         status=status, 
                         stats=stats,
                         target_users=TARGET_USERNAMES)

@app.route('/settings')
def settings():
    """Settings page"""
    # Convert float('inf') to a large number for template display
    display_share_rules = []
    for rule in SHARE_RULES:
        min_likes, max_likes, shares = rule
        # Convert float('inf') to a large number for display
        if isinstance(max_likes, float) and max_likes == float('inf'):
            max_likes = 999999999
        display_share_rules.append((min_likes, max_likes, shares))

    current_settings = {
        "telegram_token": TELEGRAM_BOT_TOKEN,
        "telegram_user_id": TELEGRAM_USER_ID,
        "tiktok_username": TIKTOK_USERNAME,
        "tiktok_password": TIKTOK_PASSWORD,
        "target_usernames": TARGET_USERNAMES,
        "recheck_interval": RECHECK_INTERVAL_HOURS,
        "share_rules": display_share_rules
    }
    return render_template('settings.html', settings=current_settings)

@app.route('/logs')
def logs():
    """Logs page"""
    return render_template('logs.html', logs=bot_logs)

@app.route('/videos')
def videos():
    """Videos history page"""
    video_data = load_previous_data()
    return render_template('videos.html', videos=video_data)

# API Routes
@app.route('/api/bot/start', methods=['POST'])
def api_start_bot():
    """API to start bot"""
    success, message = bot_manager.start_bot()
    return jsonify({"success": success, "message": message})

@app.route('/api/bot/stop', methods=['POST'])
def api_stop_bot():
    """API to stop bot"""
    success, message = bot_manager.stop_bot()
    return jsonify({"success": success, "message": message})

@app.route('/api/bot/status')
def api_bot_status():
    """API to get bot status"""
    return jsonify(bot_manager.get_status())

@app.route('/api/logs')
def api_logs():
    """API to get logs"""
    return jsonify({"logs": bot_logs[-100:]})  # Return last 100 logs

@app.route('/api/stats')
def api_stats():
    """API to get statistics"""
    video_data = load_previous_data()
    stats = {
        "total_videos": len(video_data),
        "total_shares": sum(v.get("shared_count", 0) for v in video_data.values()),
        "target_users": len(TARGET_USERNAMES),
        "recent_videos": list(video_data.items())[-10:]  # Last 10 videos
    }
    return jsonify(stats)

@app.route('/api/settings', methods=['GET'])
def api_get_settings():
    """API to get current settings"""
    settings = {
        "telegram_token": TELEGRAM_BOT_TOKEN,
        "telegram_user_id": TELEGRAM_USER_ID,
        "tiktok_username": TIKTOK_USERNAME,
        "tiktok_password": TIKTOK_PASSWORD,
        "target_usernames": TARGET_USERNAMES,
        "recheck_interval": RECHECK_INTERVAL_HOURS,
        "share_rules": SHARE_RULES
    }
    return jsonify(settings)

@app.route('/api/settings', methods=['POST'])
def api_save_settings():
    """API to save settings"""
    try:
        data = request.get_json()

        # Update .env file
        env_updates = {}

        if 'telegram_token' in data:
            env_updates['TELEGRAM_BOT_TOKEN'] = data['telegram_token']
        if 'telegram_user_id' in data:
            env_updates['TELEGRAM_USER_ID'] = str(data['telegram_user_id'])
        if 'tiktok_username' in data:
            env_updates['TIKTOK_USERNAME'] = data['tiktok_username']
        if 'tiktok_password' in data:
            env_updates['TIKTOK_PASSWORD'] = data['tiktok_password']
        if 'target_usernames' in data:
            env_updates['TARGET_USERNAMES'] = ','.join(data['target_usernames'])
        if 'recheck_interval' in data:
            env_updates['RECHECK_INTERVAL_HOURS'] = str(data['recheck_interval'])
        if 'share_rules' in data:
            rules_str = ','.join([f"{r[0]}:{r[1]}:{r[2]}" for r in data['share_rules']])
            env_updates['SHARE_RULES'] = rules_str

        # Update .env file
        update_env_file(env_updates)

        return jsonify({"success": True, "message": "Settings saved successfully"})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/test-telegram', methods=['POST'])
def api_test_telegram():
    """API to test Telegram connection"""
    try:
        asyncio.run(send_telegram_message("🧪 Test message from TikTok Bot Web UI"))
        return jsonify({"success": True, "message": "Test message sent successfully"})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

def update_env_file(updates):
    """Update .env file with new values"""
    env_file = '.env'

    # Read existing .env file
    env_vars = {}
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value

    # Update with new values
    env_vars.update(updates)

    # Write back to .env file
    with open(env_file, 'w') as f:
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")

if __name__ == '__main__':
    print("🚀 Starting TikTok Bot Web UI...")
    print("📱 Access at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
