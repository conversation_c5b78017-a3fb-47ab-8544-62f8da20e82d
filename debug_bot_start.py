#!/usr/bin/env python3
# debug_bot_start.py

"""
Debug bot startup issues
"""

import subprocess
import time
import sys
import os

def test_bot_start():
    """Test bot startup and capture output"""
    print("🐛 Debug Bot Start")
    print("=" * 50)
    
    # Test 1: Check if index.py exists
    if not os.path.exists('index.py'):
        print("❌ index.py not found!")
        return
    else:
        print("✅ index.py exists")
    
    # Test 2: Check if we can import modules
    print("\n🔍 Testing imports...")
    try:
        import config
        print("✅ config imported")
    except Exception as e:
        print(f"❌ config import failed: {e}")
        return
    
    try:
        import scrape
        print("✅ scrape imported")
    except Exception as e:
        print(f"❌ scrape import failed: {e}")
        return
    
    try:
        import telegram_logger
        print("✅ telegram_logger imported")
    except Exception as e:
        print(f"❌ telegram_logger import failed: {e}")
        return
    
    # Test 3: Try to start bot process
    print("\n🚀 Starting bot process...")
    try:
        process = subprocess.Popen(
            [sys.executable, 'index.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        print(f"✅ Process started with PID: {process.pid}")
        
        # Wait a bit and check if still running
        time.sleep(5)
        
        if process.poll() is None:
            print("✅ Process still running after 5 seconds")
            
            # Try to get some output
            try:
                stdout, stderr = process.communicate(timeout=10)
                print(f"\n📤 STDOUT:\n{stdout}")
                print(f"\n📤 STDERR:\n{stderr}")
            except subprocess.TimeoutExpired:
                print("⏳ Process still running (timeout after 10s)")
                process.kill()
                stdout, stderr = process.communicate()
                print(f"\n📤 STDOUT:\n{stdout}")
                print(f"\n📤 STDERR:\n{stderr}")
                
        else:
            print(f"❌ Process exited with code: {process.returncode}")
            stdout, stderr = process.communicate()
            print(f"\n📤 STDOUT:\n{stdout}")
            print(f"\n📤 STDERR:\n{stderr}")
            
    except Exception as e:
        print(f"❌ Failed to start process: {e}")
    
    # Test 4: Try simple_scrape.py
    print("\n🧪 Testing simple_scrape.py...")
    try:
        process = subprocess.Popen(
            [sys.executable, 'simple_scrape.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        print(f"✅ simple_scrape started with PID: {process.pid}")
        
        # Wait and get output
        try:
            stdout, stderr = process.communicate(timeout=30)
            print(f"\n📤 simple_scrape STDOUT:\n{stdout}")
            if stderr:
                print(f"\n📤 simple_scrape STDERR:\n{stderr}")
            print(f"✅ simple_scrape completed with code: {process.returncode}")
        except subprocess.TimeoutExpired:
            print("⏳ simple_scrape timeout after 30s")
            process.kill()
            stdout, stderr = process.communicate()
            print(f"\n📤 STDOUT:\n{stdout}")
            if stderr:
                print(f"\n📤 STDERR:\n{stderr}")
            
    except Exception as e:
        print(f"❌ Failed to start simple_scrape: {e}")

if __name__ == "__main__":
    test_bot_start()
