#!/usr/bin/env python3
# simple_scrape.py

"""
Simplified scrape function for testing
"""

import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
from config import TARGET_USERNAMES, AUTO_PROXY_ENABLED, PREMIUM_PROXY_API_KEY
from proxy_handler import get_auto_proxy
from telegram_logger import send_telegram_message, send_telegram_photo
from utils import (
    log_to_console,
    already_shared_videos, mark_video_shared, wait_with_retries,
    parse_like_count
)

def calculate_share_count_test(likes):
    """Test version of calculate_share_count with lower thresholds"""
    # Test rules: share videos with low likes for testing
    rules = [
        (0, 10, 1),      # 0-9 likes: 1 share
        (10, 50, 2),     # 10-49 likes: 2 shares
        (50, 100, 5),    # 50-99 likes: 5 shares
        (100, 1000, 50), # 100-999 likes: 50 shares
        (1000, 5000, 100), # 1000-4999 likes: 100 shares
        (5000, float('inf'), 150) # 5000+ likes: 150 shares
    ]

    for min_likes, max_likes, shares in rules:
        if min_likes <= likes < max_likes:
            return shares
    return 0

async def share_video_simple(video_url, share_count, page):
    """Simplified share video function"""
    try:
        log_to_console(f"📤 Attempting to share {video_url} ({share_count} times)")

        # Look for share button with multiple selectors
        share_button = None
        selectors = [
            "button[aria-label*='Share']",
            "button:has-text('Share')",
            "button[data-e2e='share-button']"
        ]

        for selector in selectors:
            share_button = await page.query_selector(selector)
            if share_button:
                log_to_console(f"✅ Found share button with selector: {selector}")
                break

        if not share_button:
            log_to_console("[WARNING] Share button not found with any selector")
            return 0
        
        actual_shares = 0
        for i in range(share_count):
            try:
                await share_button.click()
                await page.wait_for_timeout(1000)  # Wait 1 second between shares
                actual_shares += 1
                log_to_console(f"✅ Share {i+1}/{share_count} completed")
            except Exception as e:
                log_to_console(f"[WARNING] Share {i+1} failed: {e}")
                break
        
        return actual_shares
    except Exception as e:
        log_to_console(f"[ERROR] Share function failed: {e}")
        return 0

async def simple_scrape():
    """Simplified scrape function - runs once"""
    try:
        # Proxy setup
        if AUTO_PROXY_ENABLED:
            proxy = get_auto_proxy(PREMIUM_PROXY_API_KEY)
            if proxy:
                log_to_console(f"🌐 Using proxy: {proxy}")
                await send_telegram_message(f"🌐 Using proxy: {proxy}")
            else:
                log_to_console("❌ No proxy found")
                await send_telegram_message("❌ No proxy found")
                return
        else:
            proxy = None
            log_to_console("🌐 Proxy disabled - running without proxy")
            await send_telegram_message("🌐 Proxy disabled - running without proxy")

        async with async_playwright() as p:
            # Launch browser
            browser_args = [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
            
            if proxy:
                browser_args.append(f'--proxy-server=http://{proxy}')
            
            browser = await p.chromium.launch(headless=True, args=browser_args)
            context = await browser.new_context()
            page = await context.new_page()

            # Set headers
            await page.set_extra_http_headers({
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            })

            for username in TARGET_USERNAMES:
                url = f"https://www.tiktok.com/@{username}"
                try:
                    log_to_console(f"🔍 Processing @{username}")
                    await send_telegram_message(f"🔍 Processing @{username}")

                    # Navigate to user page
                    response = await page.goto(url, wait_until='load', timeout=30000)
                    log_to_console(f"✅ Loaded @{username} page (status: {response.status})")
                    
                    await page.wait_for_timeout(3000)  # Wait for page to stabilize

                    # Find videos
                    videos = await page.query_selector_all("div[data-e2e='user-post-item'] a")
                    log_to_console(f"📹 Found {len(videos)} videos for @{username}")
                    await send_telegram_message(f"📹 Found {len(videos)} videos for @{username}")

                    videos_processed = 0
                    videos_shared = 0

                    # Collect all hrefs first to avoid ElementHandle issues
                    video_urls = []
                    for i, video in enumerate(videos[:5]):  # Process max 5 videos
                        try:
                            href = await video.get_attribute("href")
                            if not href:
                                continue

                            # Handle URL
                            if href.startswith("http"):
                                video_url = href
                            else:
                                video_url = f"https://www.tiktok.com{href}"

                            video_urls.append(video_url)
                        except Exception as e:
                            log_to_console(f"[ERROR] Error getting href for video {i+1}: {e}")
                            continue

                    log_to_console(f"📋 Collected {len(video_urls)} video URLs")

                    # Now process each video URL
                    for i, video_url in enumerate(video_urls):
                        try:
                            log_to_console(f"🎥 Processing video {i+1}: {video_url}")

                            if already_shared_videos(video_url):
                                log_to_console(f"⏭️ Video already shared, skipping")
                                continue

                            # Navigate to video
                            await page.goto(video_url, wait_until='load', timeout=30000)
                            await page.wait_for_timeout(2000)

                            videos_processed += 1

                            # Get like count
                            if await wait_with_retries(page, "strong[data-e2e='like-count']", timeout=10000):
                                like_text = await page.locator("strong[data-e2e='like-count']").inner_text()
                                likes = parse_like_count(like_text)
                                log_to_console(f"👍 Video has {likes} likes")

                                share_count = calculate_share_count_test(likes)
                                if share_count > 0:
                                    actual_shares = await share_video_simple(video_url, share_count, page)
                                    if actual_shares > 0:
                                        mark_video_shared(video_url, actual_shares)
                                        videos_shared += 1
                                        await send_telegram_message(f"📢 Shared {video_url} with {actual_shares}/{share_count} shares")
                                    else:
                                        log_to_console(f"[WARNING] Failed to share {video_url}")
                                else:
                                    log_to_console(f"[INFO] Video doesn't meet share criteria ({likes} likes)")
                            else:
                                log_to_console(f"[WARNING] Could not find like count for {video_url}")

                        except Exception as video_error:
                            log_to_console(f"[ERROR] Error processing video {i+1}: {video_error}")
                            continue

                    log_to_console(f"✅ Completed @{username}: {videos_processed} processed, {videos_shared} shared")
                    await send_telegram_message(f"✅ @{username}: {videos_processed} processed, {videos_shared} shared")

                except PlaywrightTimeoutError:
                    log_to_console(f"[TIMEOUT] Could not load {url}")
                    await send_telegram_message(f"⏰ Timeout loading @{username}")
                except Exception as e:
                    log_to_console(f"[ERROR] Exception for @{username}: {e}")
                    try:
                        page_path = f"error_{username}.png"
                        await page.screenshot(path=page_path)
                        await send_telegram_photo(page_path, f"❌ Error for @{username}: {str(e)[:100]}")
                    except:
                        pass

            await browser.close()
            log_to_console("✅ Simple scrape completed")
            await send_telegram_message("✅ Simple scrape completed")

    except Exception as e:
        log_to_console(f"[ERROR] Simple scrape failed: {e}")
        await send_telegram_message(f"❌ Simple scrape failed: {str(e)[:100]}")

if __name__ == "__main__":
    asyncio.run(simple_scrape())
