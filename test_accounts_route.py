#!/usr/bin/env python3
# test_accounts_route.py

"""
Test accounts route
"""

from flask import Flask, render_template, jsonify
import os

app = Flask(__name__)

@app.route('/')
def home():
    return "Test Flask App - Home"

@app.route('/accounts')
def accounts():
    """Test accounts route"""
    try:
        # Check if template exists
        template_path = os.path.join('templates', 'accounts.html')
        if os.path.exists(template_path):
            return render_template('accounts.html')
        else:
            return f"Template not found: {template_path}"
    except Exception as e:
        return f"Error: {str(e)}"

@app.route('/api/accounts')
def api_accounts():
    """Test API route"""
    return jsonify({
        "success": True,
        "accounts": [
            {
                "username": "test_user",
                "email": "<EMAIL>",
                "status": "created",
                "created_at": "2025-07-07T15:00:00"
            }
        ]
    })

if __name__ == '__main__':
    print("🧪 Testing accounts route...")
    print("📱 Access at: http://localhost:5001")
    print("🔗 Accounts page: http://localhost:5001/accounts")
    print("🔗 API test: http://localhost:5001/api/accounts")
    app.run(debug=True, port=5001)
