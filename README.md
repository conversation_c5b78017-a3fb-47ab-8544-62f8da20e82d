# TikTok Bot - Automated Video Sharing

Một bot tự động để theo dõi và share video TikTok dựa trên số lượng like.

## ⚠️ Lưu ý quan trọng

Bot này được tạo cho mục đích giáo dục. Vui lòng tuân thủ các điều khoản sử dụng của TikTok và các luật pháp địa phương.

## 🚀 Tính năng

- ✅ Theo dõi các TikTok user được chỉ định
- ✅ Tự động share video dựa trên số lượng like
- ✅ Sử dụng proxy để tránh bị block
- ✅ Gửi thông báo qua Telegram
- ✅ Lưu trữ lịch sử video đã share
- ✅ Error handling và logging chi tiết
- ✅ Cấu hình linh hoạt qua environment variables

## 📋 Yêu cầu

- Python 3.11+
- Playwright
- Telegram Bot Token
- Proxy list (tù<PERSON> chọ<PERSON>)

## 🛠️ Cài đặt

### 1. Clone repository
```bash
git clone <repository-url>
cd tiktok-bot
```

### 2. Cài đặt dependencies
```bash
pip install -r requirements.txt
playwright install chromium
```

### 3. Cấu hình environment variables

Tạo file `.env` từ `.env.example`:
```bash
cp .env.example .env
```

Chỉnh sửa file `.env`:
```env
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_USER_ID=your_telegram_user_id_here

# Target usernames to monitor (comma-separated)
TARGET_USERNAMES=username1,username2

# Share rules (format: min_likes:max_likes:shares,...)
SHARE_RULES=0:100:0,100:1000:50,1000:5000:100,5000:999999999:150
```

### 4. Chuẩn bị proxy list (tùy chọn)

Tạo file `proxy.txt` với format:
```
ip:port
ip:port
user:pass@ip:port
```

## 🎯 Cách sử dụng

### Chạy bot
```bash
python index.py
```

### Chạy với Docker
```bash
docker build -t tiktok-bot .
docker run -d --env-file .env tiktok-bot
```

### Test cấu hình
```bash
python config.py
```

## ⚙️ Cấu hình

### Share Rules
Format: `min_likes:max_likes:shares`

Ví dụ:
- `0:100:0` - Không share video có dưới 100 likes
- `100:1000:50` - Share 50 lần cho video 100-1000 likes
- `1000:5000:100` - Share 100 lần cho video 1000-5000 likes
- `5000:999999999:150` - Share 150 lần cho video trên 5000 likes

### Target Usernames
Danh sách username TikTok cần theo dõi, phân cách bằng dấu phẩy.

## 📁 Cấu trúc file

```
├── index.py              # Entry point
├── scrape.py             # Logic chính của bot
├── config.py             # Cấu hình
├── utils.py              # Utility functions
├── telegram_logger.py    # Telegram integration
├── proxy_handler.py      # Proxy management
├── requirements.txt      # Dependencies
├── Dockerfile           # Docker configuration
├── .env.example         # Environment variables template
└── README.md           # Documentation
```

## 🔧 Troubleshooting

### Bot không chạy
1. Kiểm tra Python version (>= 3.11)
2. Cài đặt lại dependencies: `pip install -r requirements.txt`
3. Cài đặt Playwright browsers: `playwright install chromium`

### Không nhận được thông báo Telegram
1. Kiểm tra `TELEGRAM_BOT_TOKEN` và `TELEGRAM_USER_ID`
2. Đảm bảo bot đã được start với `/start` command

### Proxy không hoạt động
1. Kiểm tra format proxy trong `proxy.txt`
2. Test proxy thủ công
3. Sử dụng proxy từ nguồn đáng tin cậy

### Video không được share
1. Kiểm tra TikTok selectors (có thể thay đổi)
2. Kiểm tra proxy có bị block không
3. Xem log để debug

## 📊 Monitoring

Bot sẽ ghi log vào:
- Console output
- File `log.txt`
- Telegram messages

## 🔒 Bảo mật

- Không commit file `.env` vào git
- Sử dụng proxy đáng tin cậy
- Thường xuyên thay đổi credentials
- Monitor bot activity

## 📝 License

Dự án này chỉ dành cho mục đích giáo dục. Vui lòng sử dụng có trách nhiệm.
