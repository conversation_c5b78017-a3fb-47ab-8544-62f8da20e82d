/**
 * TikTok Bot Manager - Main JavaScript File
 */

// Global variables
let botStatusInterval;
let notificationPermission = false;

// Initialize app when document is ready
$(document).ready(function() {
    initializeApp();
});

function initializeApp() {
    // Request notification permission
    requestNotificationPermission();
    
    // Start status monitoring
    startStatusMonitoring();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize event listeners
    initializeEventListeners();
    
    // Add fade-in animation to cards
    $('.card').addClass('fade-in');
    
    console.log('TikTok Bot Manager initialized');
}

function requestNotificationPermission() {
    if ('Notification' in window) {
        Notification.requestPermission().then(function(permission) {
            notificationPermission = permission === 'granted';
        });
    }
}

function startStatusMonitoring() {
    // Update status immediately
    updateBotStatus();
    
    // Update every 5 seconds
    botStatusInterval = setInterval(updateBotStatus, 5000);
}

function updateBotStatus() {
    $.get('/api/bot/status')
        .done(function(data) {
            updateStatusIndicators(data);
        })
        .fail(function() {
            updateStatusIndicators({ status: 'unknown' });
        });
}

function updateStatusIndicators(statusData) {
    const status = statusData.status || 'unknown';
    const indicator = $('#bot-status-indicator');
    const statusBadge = $('#bot-status');
    
    // Remove all status classes
    indicator.removeClass('bg-success bg-danger bg-warning bg-secondary');
    if (statusBadge.length) {
        statusBadge.removeClass('bg-success bg-danger bg-warning bg-secondary');
    }
    
    // Update based on status
    switch(status) {
        case 'running':
            indicator.addClass('bg-success');
            if (statusBadge.length) statusBadge.addClass('bg-success');
            indicator.html('<i class="fas fa-circle me-1 status-running"></i>Running');
            break;
        case 'stopped':
            indicator.addClass('bg-danger');
            if (statusBadge.length) statusBadge.addClass('bg-danger');
            indicator.html('<i class="fas fa-circle me-1"></i>Stopped');
            break;
        default:
            indicator.addClass('bg-warning');
            if (statusBadge.length) statusBadge.addClass('bg-warning');
            indicator.html('<i class="fas fa-circle me-1"></i>Unknown');
    }
    
    // Update status text
    if (statusBadge.length) {
        statusBadge.text(status.charAt(0).toUpperCase() + status.slice(1));
    }
    
    // Update uptime if available
    if (statusData.uptime && $('#bot-uptime').length) {
        $('#bot-uptime').text(statusData.uptime);
    }
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function initializeEventListeners() {
    // Global error handler for AJAX requests
    $(document).ajaxError(function(event, xhr, settings, thrownError) {
        if (xhr.status !== 0) { // Ignore aborted requests
            showNotification('error', 'Network error occurred');
        }
    });
    
    // Handle form submissions with loading states
    $('form').on('submit', function(e) {
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        
        if (submitBtn.length) {
            const originalText = submitBtn.html();
            submitBtn.prop('disabled', true)
                    .html('<i class="fas fa-spinner fa-spin me-2"></i>Saving...');
            
            // Re-enable after 3 seconds (fallback)
            setTimeout(function() {
                submitBtn.prop('disabled', false).html(originalText);
            }, 3000);
        }
    });
    
    // Auto-hide alerts after 5 seconds
    $(document).on('shown.bs.alert', '.alert', function() {
        const alert = $(this);
        setTimeout(function() {
            alert.alert('close');
        }, 5000);
    });
}

// Bot Control Functions
function startBot() {
    return controlBot('start');
}

function stopBot() {
    return controlBot('stop');
}

function controlBot(action) {
    const button = $(`#${action}-bot`);
    const originalText = button.html();
    
    // Update button state
    button.prop('disabled', true)
          .html(`<i class="fas fa-spinner fa-spin me-2"></i>${action === 'start' ? 'Starting' : 'Stopping'}...`);
    
    return $.post(`/api/bot/${action}`)
        .done(function(data) {
            if (data.success) {
                showNotification('success', data.message);
                updateBotStatus(); // Immediate status update
            } else {
                showNotification('error', data.message);
            }
        })
        .fail(function() {
            showNotification('error', `Failed to ${action} bot`);
        })
        .always(function() {
            // Reset button state
            setTimeout(function() {
                button.prop('disabled', false).html(originalText);
            }, 1000);
        });
}

// Notification Functions
function showNotification(type, message, title = '') {
    // Show in-page alert
    showAlert(type, message);
    
    // Show browser notification if permitted
    if (notificationPermission && title) {
        showBrowserNotification(title, message, type);
    }
}

function showAlert(type, message) {
    const alertClass = getAlertClass(type);
    const icon = getAlertIcon(type);
    
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="${icon} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    // Insert at top of main container
    $('.container-fluid').prepend(alert);
    
    // Auto-hide after 5 seconds
    setTimeout(function() {
        alert.alert('close');
    }, 5000);
}

function showBrowserNotification(title, message, type) {
    if (!notificationPermission) return;
    
    const icon = getNotificationIcon(type);
    
    const notification = new Notification(title, {
        body: message,
        icon: icon,
        badge: '/static/favicon.ico'
    });
    
    // Auto-close after 5 seconds
    setTimeout(function() {
        notification.close();
    }, 5000);
}

function getAlertClass(type) {
    switch(type) {
        case 'success': return 'alert-success';
        case 'error': return 'alert-danger';
        case 'warning': return 'alert-warning';
        case 'info': 
        default: return 'alert-info';
    }
}

function getAlertIcon(type) {
    switch(type) {
        case 'success': return 'fas fa-check-circle';
        case 'error': return 'fas fa-exclamation-circle';
        case 'warning': return 'fas fa-exclamation-triangle';
        case 'info':
        default: return 'fas fa-info-circle';
    }
}

function getNotificationIcon(type) {
    // Return appropriate icon URL based on type
    return '/static/favicon.ico'; // Fallback to favicon
}

// Utility Functions
function formatUptime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
        return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${secs}s`;
    } else {
        return `${secs}s`;
    }
}

function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification('success', 'Copied to clipboard');
    }).catch(function() {
        showNotification('error', 'Failed to copy to clipboard');
    });
}

function downloadFile(content, filename, contentType = 'text/plain') {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Settings Functions
function saveSettings(formData, endpoint = '/api/settings') {
    return $.ajax({
        url: endpoint,
        method: 'POST',
        data: formData,
        contentType: 'application/json'
    });
}

function loadSettings(endpoint = '/api/settings') {
    return $.get(endpoint);
}

// Statistics Functions
function updateStatistics() {
    $.get('/api/stats')
        .done(function(data) {
            updateStatisticsDisplay(data);
        })
        .fail(function() {
            console.error('Failed to load statistics');
        });
}

function updateStatisticsDisplay(stats) {
    // Update various statistics on the page
    if (stats.total_videos !== undefined) {
        $('.stat-total-videos').text(formatNumber(stats.total_videos));
    }
    if (stats.total_shares !== undefined) {
        $('.stat-total-shares').text(formatNumber(stats.total_shares));
    }
    if (stats.target_users !== undefined) {
        $('.stat-target-users').text(stats.target_users);
    }
}

// Cleanup functions
function cleanup() {
    if (botStatusInterval) {
        clearInterval(botStatusInterval);
    }
}

// Handle page unload
$(window).on('beforeunload', function() {
    cleanup();
});

// Handle visibility change (pause updates when tab is not visible)
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        cleanup();
    } else {
        startStatusMonitoring();
    }
});

// Export functions for global access
window.TikTokBot = {
    startBot,
    stopBot,
    showNotification,
    showAlert,
    updateBotStatus,
    updateStatistics,
    copyToClipboard,
    downloadFile,
    saveSettings,
    loadSettings
};
