#!/usr/bin/env python3
"""
Manual Test for Target Users
Script đơn giản để test việc lưu Target Users
"""

import os
import requests
import json

def read_current_target_users():
    """Đọc Target Users hiện tại từ .env"""
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if line.startswith('TARGET_USERNAMES='):
                    usernames = line.split('=', 1)[1].strip()
                    return usernames.split(',') if usernames else []
    return []

def test_target_users_save():
    """Test việc lưu Target Users"""
    print("🧪 Manual Test: Target Users Save")
    print("=" * 40)
    
    # Hiển thị Target Users hiện tại
    current_users = read_current_target_users()
    print(f"📋 Current Target Users: {current_users}")
    
    # Nhập Target Users mới
    print("\n✏️ Enter new Target Users (comma-separated):")
    print("Example: user1, user2, user3")
    new_users_input = input("👤 New users: ").strip()
    
    if not new_users_input:
        print("❌ No users entered. Exiting.")
        return
    
    # Parse usernames
    new_users = [u.strip().replace('@', '') for u in new_users_input.split(',') if u.strip()]
    print(f"📝 Parsed users: {new_users}")
    
    # Test API call
    print("\n🔄 Testing API call...")
    
    try:
        # Prepare data
        data = {"target_usernames": new_users}
        
        # Make API call
        response = requests.post(
            "http://localhost:5000/api/settings/target-users",
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📡 API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ Success: {result['message']}")
                
                # Verify by reading .env again
                updated_users = read_current_target_users()
                print(f"🔍 Updated Target Users: {updated_users}")
                
                if set(updated_users) == set(new_users):
                    print("🎉 Target Users saved successfully!")
                    return True
                else:
                    print("❌ Target Users not saved correctly")
                    print(f"   Expected: {new_users}")
                    print(f"   Got: {updated_users}")
                    return False
            else:
                print(f"❌ API Error: {result.get('message')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Web UI server not running")
        print("💡 Start the Web UI first: python app.py")
        return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_direct_env_update():
    """Test direct .env file update"""
    print("\n🧪 Manual Test: Direct .env Update")
    print("=" * 40)
    
    # Backup current .env
    if os.path.exists('.env'):
        import shutil
        shutil.copy('.env', '.env.manual_backup')
        print("💾 Backed up .env file")
    
    try:
        # Read current .env
        env_vars = {}
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key] = value
        
        # Get new users
        print("✏️ Enter test usernames for direct .env update:")
        test_users_input = input("👤 Test users: ").strip()
        
        if test_users_input:
            test_users = [u.strip().replace('@', '') for u in test_users_input.split(',') if u.strip()]
            
            # Update TARGET_USERNAMES
            env_vars['TARGET_USERNAMES'] = ','.join(test_users)
            
            # Write back to .env
            with open('.env', 'w') as f:
                for key, value in env_vars.items():
                    f.write(f"{key}={value}\n")
            
            print(f"✅ Updated .env file with: {test_users}")
            
            # Verify
            updated_users = read_current_target_users()
            print(f"🔍 Verification: {updated_users}")
            
            if set(updated_users) == set(test_users):
                print("🎉 Direct .env update successful!")
                return True
            else:
                print("❌ Direct .env update failed")
                return False
        else:
            print("❌ No users entered")
            return False
            
    except Exception as e:
        print(f"❌ Exception during direct update: {e}")
        return False
    finally:
        # Restore backup
        if os.path.exists('.env.manual_backup'):
            import shutil
            shutil.copy('.env.manual_backup', '.env')
            os.remove('.env.manual_backup')
            print("🔄 Restored .env from backup")

def main():
    """Main function"""
    print("🎯 Target Users Manual Test")
    print("=" * 50)
    
    print("Choose test type:")
    print("1. Test API call (requires Web UI running)")
    print("2. Test direct .env update")
    print("3. Both tests")
    
    choice = input("\nEnter choice (1/2/3): ").strip()
    
    if choice == "1":
        success = test_target_users_save()
    elif choice == "2":
        success = test_direct_env_update()
    elif choice == "3":
        print("\n" + "="*25 + " TEST 1 " + "="*25)
        success1 = test_target_users_save()
        print("\n" + "="*25 + " TEST 2 " + "="*25)
        success2 = test_direct_env_update()
        success = success1 and success2
    else:
        print("❌ Invalid choice")
        return 1
    
    print("\n" + "="*50)
    if success:
        print("🎉 All tests passed!")
        print("💡 Target Users functionality should work correctly")
    else:
        print("❌ Some tests failed")
        print("🔧 Check the issues above and fix them")
    
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
