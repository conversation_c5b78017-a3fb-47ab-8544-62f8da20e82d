#!/usr/bin/env python3
# test_account_creation.py

"""
Test account creation directly
"""

import asyncio
from tiktok_account_manager import TikTokAccountManager

async def test_create_account():
    """Test creating a single account"""
    print("🧪 Testing TikTok Account Creation")
    print("=" * 50)
    
    try:
        # Initialize account manager
        manager = TikTokAccountManager()
        print("✅ Account manager initialized")
        
        # Generate test account data
        account_data = manager.generate_account_data()
        print(f"✅ Generated account data:")
        print(f"   Username: {account_data['username']}")
        print(f"   Email: {account_data['email']}")
        print(f"   Password: {account_data['password']}")
        
        # Test without actually creating (to avoid browser opening)
        print("\n🔍 Testing account data generation...")
        for i in range(3):
            test_data = manager.generate_account_data()
            print(f"   {i+1}. {test_data['username']} - {test_data['email']}")
        
        # Save test account to file
        account_data['status'] = 'test'
        manager.accounts.append(account_data)
        manager.save_accounts()
        print(f"\n✅ Test account saved to {manager.accounts_file}")
        
        # Load and display all accounts
        all_accounts = manager.get_accounts()
        print(f"\n📋 Total accounts in file: {len(all_accounts)}")
        for account in all_accounts:
            print(f"   - {account['username']} ({account['status']})")
        
        print("\n🎉 Account creation test completed successfully!")
        print("\n💡 To create real account:")
        print("   1. Use Web UI: http://localhost:5000/accounts")
        print("   2. Or run: python tiktok_account_manager.py")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_create_account())
