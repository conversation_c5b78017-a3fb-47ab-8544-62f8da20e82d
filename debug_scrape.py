#!/usr/bin/env python3
# debug_scrape.py

"""
Debug version of scrape to identify issues
"""

import asyncio
from playwright.async_api import async_playwright
from config import TARGET_USERNAMES, AUTO_PROXY_ENABLED
from telegram_logger import send_telegram_message

async def debug_scrape():
    """Debug version of scrape function"""
    print("🐛 Debug Scrape Starting...")
    print(f"Target users: {TARGET_USERNAMES}")
    print(f"Auto proxy enabled: {AUTO_PROXY_ENABLED}")
    
    try:
        await send_telegram_message("🐛 Debug scrape starting...")
        
        async with async_playwright() as p:
            print("🌐 Launching browser...")
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ]
            )
            
            print("✅ Browser launched")
            context = await browser.new_context()
            page = await context.new_page()
            
            # Set headers
            await page.set_extra_http_headers({
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            })
            
            print("✅ Page created")
            
            for username in TARGET_USERNAMES:
                url = f"https://www.tiktok.com/@{username}"
                print(f"🔍 Processing @{username}")
                print(f"🌐 URL: {url}")
                
                try:
                    print("📡 Navigating to page...")
                    response = await page.goto(url, wait_until='load', timeout=30000)
                    print(f"✅ Page loaded with status: {response.status}")
                    
                    # Wait a bit for page to stabilize
                    await page.wait_for_timeout(3000)
                    
                    # Check page title
                    title = await page.title()
                    print(f"📄 Page title: {title}")
                    
                    # Look for videos
                    print("🔍 Looking for videos...")
                    videos = await page.query_selector_all("div[data-e2e='user-post-item'] a")
                    print(f"📹 Found {len(videos)} videos")
                    
                    if len(videos) > 0:
                        print("✅ Videos found! Bot should work.")
                        await send_telegram_message(f"✅ Found {len(videos)} videos for @{username}")
                        
                        # Test first video
                        try:
                            first_video = videos[0]
                            href = await first_video.get_attribute("href")
                            print(f"🎥 First video href: {href}")
                            
                            if href:
                                if href.startswith("http"):
                                    video_url = href
                                else:
                                    video_url = f"https://www.tiktok.com{href}"
                                
                                print(f"🎥 First video URL: {video_url}")
                                await send_telegram_message(f"🎥 Sample video: {video_url}")
                            
                        except Exception as video_error:
                            print(f"❌ Error getting first video: {video_error}")
                    else:
                        print("❌ No videos found")
                        await send_telegram_message(f"❌ No videos found for @{username}")
                    
                except Exception as e:
                    print(f"❌ Error processing @{username}: {e}")
                    await send_telegram_message(f"❌ Error for @{username}: {str(e)[:100]}")
            
            print("🔄 Closing browser...")
            await browser.close()
            print("✅ Debug scrape completed")
            await send_telegram_message("✅ Debug scrape completed")
            
    except Exception as e:
        print(f"❌ Debug scrape failed: {e}")
        await send_telegram_message(f"❌ Debug scrape failed: {str(e)[:100]}")

if __name__ == "__main__":
    asyncio.run(debug_scrape())
