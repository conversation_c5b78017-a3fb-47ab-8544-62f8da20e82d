#!/usr/bin/env python3
# quick_test_bot.py

"""
Quick test for TikTok bot configuration
"""

import asyncio
from proxy_handler import get_auto_proxy
from config import AUTO_PROXY_ENABLED, PREMIUM_PROXY_API_KEY, TARGET_USERNAMES
from telegram_logger import send_telegram_message

async def quick_test():
    """Quick test of bot configuration"""
    print("🚀 Quick TikTok Bot Test")
    print("=" * 40)
    
    # Test 1: Auto proxy
    print("\n🌐 Testing auto proxy...")
    if AUTO_PROXY_ENABLED:
        proxy = get_auto_proxy(PREMIUM_PROXY_API_KEY)
    else:
        proxy = get_auto_proxy()
    
    if proxy:
        print(f"✅ Auto proxy: {proxy}")
        await send_telegram_message(f"🌐 Auto proxy ready: {proxy}")
    else:
        print("❌ No proxy found")
        await send_telegram_message("❌ No proxy found")
    
    # Test 2: Target usernames
    print(f"\n🎯 Target usernames: {TARGET_USERNAMES}")
    if isinstance(TARGET_USERNAMES, list):
        target_list = TARGET_USERNAMES
    else:
        target_list = TARGET_USERNAMES.split(',') if TARGET_USERNAMES else []
    
    if target_list:
        print(f"✅ Targets: {', '.join(target_list)}")
        await send_telegram_message(f"🎯 Targets: {', '.join(target_list)}")
    else:
        print("❌ No targets configured")
        await send_telegram_message("❌ No targets configured")
    
    # Test 3: Telegram
    print("\n📱 Testing Telegram...")
    await send_telegram_message("🤖 TikTok Bot quick test completed!")
    print("✅ Telegram working")
    
    print("\n🎉 Quick test completed!")
    print("\n💡 To run the bot:")
    print("   python index.py")
    print("\n💡 To use Web UI:")
    print("   python run_ui.py")

if __name__ == "__main__":
    asyncio.run(quick_test())
