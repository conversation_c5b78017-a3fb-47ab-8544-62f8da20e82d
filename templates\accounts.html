{% extends "base.html" %}

{% block title %}Account Management - TikTok Bot{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-users me-2"></i>TikTok Account Management</h1>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAccountModal">
                    <i class="fas fa-plus me-2"></i>Create Account
                </button>
            </div>
        </div>
    </div>

    <!-- Account Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="total-accounts">0</h4>
                            <p class="mb-0">Total Accounts</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="active-accounts">0</h4>
                            <p class="mb-0">Active Accounts</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="pending-accounts">0</h4>
                            <p class="mb-0">Pending Accounts</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="failed-accounts">0</h4>
                            <p class="mb-0">Failed Accounts</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Accounts Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>Account List</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="accounts-table">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="accounts-tbody">
                                <!-- Accounts will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Account Modal -->
<div class="modal fade" id="createAccountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create TikTok Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="create-account-form">
                    <div class="mb-3">
                        <label for="account-count" class="form-label">Number of Accounts</label>
                        <input type="number" class="form-control" id="account-count" min="1" max="5" value="1">
                        <div class="form-text">Maximum 5 accounts at once</div>
                    </div>
                    <div class="mb-3">
                        <label for="account-proxy" class="form-label">Proxy (Optional)</label>
                        <input type="text" class="form-control" id="account-proxy" placeholder="ip:port">
                        <div class="form-text">Leave empty to use auto proxy</div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> Account creation requires manual CAPTCHA solving. 
                        A browser window will open for each account.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="create-account-btn">Create Account(s)</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // Load accounts on page load
    loadAccounts();
    
    // Refresh every 30 seconds
    setInterval(loadAccounts, 30000);
    
    // Create account button
    $('#create-account-btn').click(function() {
        createAccount();
    });
});

function loadAccounts() {
    $.get('/api/accounts', function(data) {
        if (data.success) {
            updateAccountStats(data.accounts);
            updateAccountTable(data.accounts);
        } else {
            showAlert('danger', 'Failed to load accounts: ' + data.message);
        }
    }).fail(function() {
        showAlert('danger', 'Failed to connect to server');
    });
}

function updateAccountStats(accounts) {
    const total = accounts.length;
    const active = accounts.filter(a => a.status === 'created').length;
    const pending = accounts.filter(a => a.status === 'pending').length;
    const failed = accounts.filter(a => a.status === 'failed' || a.status === 'error').length;
    
    $('#total-accounts').text(total);
    $('#active-accounts').text(active);
    $('#pending-accounts').text(pending);
    $('#failed-accounts').text(failed);
}

function updateAccountTable(accounts) {
    const tbody = $('#accounts-tbody');
    tbody.empty();
    
    if (accounts.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="5" class="text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    No accounts found. Create your first account!
                </td>
            </tr>
        `);
        return;
    }
    
    accounts.forEach(function(account) {
        const statusBadge = getStatusBadge(account.status);
        const createdDate = new Date(account.created_at).toLocaleDateString();
        
        tbody.append(`
            <tr>
                <td>
                    <strong>${account.username}</strong>
                    ${account.profile_url ? `<br><small><a href="${account.profile_url}" target="_blank">View Profile</a></small>` : ''}
                </td>
                <td>${account.email}</td>
                <td>${statusBadge}</td>
                <td>${createdDate}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewAccount('${account.username}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteAccount('${account.username}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `);
    });
}

function getStatusBadge(status) {
    const badges = {
        'created': '<span class="badge bg-success">Created</span>',
        'pending': '<span class="badge bg-warning">Pending</span>',
        'failed': '<span class="badge bg-danger">Failed</span>',
        'error': '<span class="badge bg-danger">Error</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function createAccount() {
    const count = parseInt($('#account-count').val());
    const proxy = $('#account-proxy').val().trim();
    
    if (count < 1 || count > 5) {
        showAlert('warning', 'Please enter a valid number of accounts (1-5)');
        return;
    }
    
    const btn = $('#create-account-btn');
    const originalText = btn.html();
    btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Creating...');
    
    const data = { count: count };
    if (proxy) {
        data.proxy = proxy;
    }
    
    $.post('/api/accounts/create', JSON.stringify(data), function(response) {
        if (response.success) {
            showAlert('success', response.message);
            $('#createAccountModal').modal('hide');
            setTimeout(loadAccounts, 2000); // Reload accounts after 2 seconds
        } else {
            showAlert('danger', 'Failed to create account: ' + response.message);
        }
    }, 'json').fail(function() {
        showAlert('danger', 'Failed to connect to server');
    }).always(function() {
        btn.prop('disabled', false).html(originalText);
    });
}

function viewAccount(username) {
    // TODO: Implement account details view
    showAlert('info', 'Account details for: ' + username);
}

function deleteAccount(username) {
    if (confirm('Are you sure you want to delete account: ' + username + '?')) {
        // TODO: Implement account deletion
        showAlert('info', 'Delete functionality coming soon');
    }
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert at the top
    $('body').prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
