{% extends "base.html" %}

{% block title %}Dashboard - TikTok Bot Manager{% endblock %}

{% block content %}
<div class="row">
    <!-- Bot Control Panel -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-robot me-2"></i>Bot Control</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button id="start-bot" class="btn btn-success btn-lg">
                        <i class="fas fa-play me-2"></i>Start Bot (Normal)
                    </button>
                    <button id="start-manual-captcha" class="btn btn-warning btn-lg">
                        <i class="fas fa-eye me-2"></i>Start Bot (Manual CAPTCHA)
                    </button>
                    <button id="start-bypass-captcha" class="btn btn-info btn-lg">
                        <i class="fas fa-shield-alt me-2"></i>Start Bot (Bypass CAPTCHA)
                    </button>
                    <button id="stop-bot" class="btn btn-danger btn-lg">
                        <i class="fas fa-stop me-2"></i>Stop Bot
                    </button>
                </div>

                <div class="mt-3">
                    <small class="text-muted">
                        <strong>Normal:</strong> Standard bot operation<br>
                        <strong>Manual CAPTCHA:</strong> Opens browser for manual CAPTCHA solving<br>
                        <strong>Bypass CAPTCHA:</strong> Attempts automatic CAPTCHA bypass
                    </small>
                </div>
                
                <hr>
                
                <!-- Bot Status -->
                <div class="text-center">
                    <h6>Current Status</h6>
                    <span id="bot-status" class="badge bg-secondary fs-6">
                        {{ status.status|title }}
                    </span>
                    
                    {% if status.uptime %}
                    <div class="mt-2">
                        <small class="text-muted">
                            Uptime: <span id="bot-uptime">{{ status.uptime }}</span>
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics -->
    <div class="col-md-8">
        <div class="row">
            <!-- TikTok Access Status -->
            <div class="col-md-12 mb-3">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-globe me-2"></i>TikTok Access Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <span id="tiktok-status" class="badge bg-warning">Checking...</span>
                                    <div class="mt-1">
                                        <small class="text-muted">Access Status</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <span id="proxy-count" class="badge bg-secondary">0</span>
                                    <div class="mt-1">
                                        <small class="text-muted">Proxies Available</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <button id="test-access" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-vial me-1"></i>Test Access
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-video fa-2x text-primary mb-2"></i>
                        <h4 class="card-title">{{ stats.total_videos }}</h4>
                        <p class="card-text">Total Videos</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-share fa-2x text-success mb-2"></i>
                        <h4 class="card-title">{{ stats.total_shares }}</h4>
                        <p class="card-text">Total Shares</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-users fa-2x text-info mb-2"></i>
                        <h4 class="card-title">{{ stats.target_users }}</h4>
                        <p class="card-text">Target Users</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-rules fa-2x text-warning mb-2"></i>
                        <h4 class="card-title">{{ stats.share_rules }}</h4>
                        <p class="card-text">Share Rules</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- Target Users -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bullseye me-2"></i>Target Users</h5>
            </div>
            <div class="card-body">
                {% if target_users %}
                    <div class="list-group">
                        {% for user in target_users %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fab fa-tiktok me-2"></i>
                                <strong>@{{ user }}</strong>
                            </div>
                            <a href="https://www.tiktok.com/@{{ user }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-user-slash fa-3x mb-3"></i>
                        <p>No target users configured</p>
                        <a href="{{ url_for('settings') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Users
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Activity</h5>
            </div>
            <div class="card-body">
                <div id="recent-logs" style="max-height: 300px; overflow-y: auto;">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin"></i>
                        Loading recent activity...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('settings') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-cog me-2"></i>Configure Settings
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('logs') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-file-alt me-2"></i>View Full Logs
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('videos') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-video me-2"></i>Video History
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button id="test-telegram" class="btn btn-outline-warning w-100">
                            <i class="fab fa-telegram me-2"></i>Test Telegram
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // Load initial data
    updateBotStatus();
    loadRecentLogs();
    loadTikTokStatus();

    // Update status every 5 seconds
    setInterval(updateBotStatus, 5000);
    setInterval(loadRecentLogs, 10000);
    setInterval(loadTikTokStatus, 30000); // Refresh TikTok status every 30 seconds

    // Bot control buttons
    $('#start-bot').click(function() {
        controlBot('start');
    });

    $('#start-manual-captcha').click(function() {
        if (confirm('This will open a browser window for manual CAPTCHA solving. Continue?')) {
            controlBot('start-manual-captcha');
        }
    });

    $('#start-bypass-captcha').click(function() {
        if (confirm('This will attempt automatic CAPTCHA bypass. Continue?')) {
            controlBot('start-bypass-captcha');
        }
    });

    $('#stop-bot').click(function() {
        controlBot('stop');
    });

    // TikTok access test button
    $('#test-access').click(function() {
        testTikTokAccess();
    });

    $('#test-telegram').click(function() {
        testTelegram();
    });
});

function updateBotStatus() {
    $.get('/api/bot/status', function(data) {
        const status = data.status;
        const badge = $('#bot-status');
        const indicator = $('#bot-status-indicator');
        
        badge.text(status.charAt(0).toUpperCase() + status.slice(1));
        indicator.html('<i class="fas fa-circle me-1"></i>' + status.charAt(0).toUpperCase() + status.slice(1));
        
        if (status === 'running' || status === 'running_manual_captcha' || status === 'running_bypass_captcha') {
            badge.removeClass('bg-secondary bg-danger').addClass('bg-success');
            indicator.removeClass('bg-secondary bg-danger').addClass('bg-success');
            $('#start-bot').prop('disabled', true);
            $('#start-manual-captcha').prop('disabled', true);
            $('#start-bypass-captcha').prop('disabled', true);
            $('#stop-bot').prop('disabled', false);
        } else {
            badge.removeClass('bg-success bg-secondary').addClass('bg-danger');
            indicator.removeClass('bg-success bg-secondary').addClass('bg-danger');
            $('#start-bot').prop('disabled', false);
            $('#start-manual-captcha').prop('disabled', false);
            $('#start-bypass-captcha').prop('disabled', false);
            $('#stop-bot').prop('disabled', true);
        }
        
        if (data.uptime) {
            $('#bot-uptime').text(data.uptime);
        }
    });
}

function loadRecentLogs() {
    $.get('/api/logs', function(data) {
        const logsContainer = $('#recent-logs');
        logsContainer.empty();

        if (data.logs && data.logs.length > 0) {
            data.logs.slice(-5).forEach(function(log) {
                const logClass = log.type === 'error' ? 'text-danger' : 'text-muted';
                logsContainer.append(
                    '<div class="small mb-1">' +
                    '<span class="text-muted">' + log.timestamp + '</span> ' +
                    '<span class="' + logClass + '">' + log.message + '</span>' +
                    '</div>'
                );
            });
        } else {
            logsContainer.html('<div class="text-center text-muted">No recent activity</div>');
        }
    });
}

function loadTikTokStatus() {
    $.get('/api/stats', function(data) {
        // Update TikTok access status
        if (data.tiktok_access) {
            const status = data.tiktok_access.status;
            const statusBadge = $('#tiktok-status');

            if (status === 'accessible') {
                statusBadge.removeClass('bg-warning bg-danger').addClass('bg-success').text('Accessible');
            } else if (status === 'login_required') {
                statusBadge.removeClass('bg-success bg-warning').addClass('bg-danger').text('Login Required');
            } else if (status === 'captcha_detected') {
                statusBadge.removeClass('bg-success bg-warning').addClass('bg-danger').text('CAPTCHA Detected');
            } else {
                statusBadge.removeClass('bg-success bg-danger').addClass('bg-warning').text('Unknown');
            }
        }

        // Update proxy count
        if (data.proxy_status) {
            $('#proxy-count').text(data.proxy_status.total_proxies);
        }

        // Update bot health indicator if exists
        if (data.bot_health) {
            updateBotHealth(data.bot_health);
        }
    }).fail(function() {
        $('#tiktok-status').removeClass('bg-success bg-warning').addClass('bg-danger').text('Error');
    });
}

function updateBotHealth(health) {
    // Add health indicator to the page if it doesn't exist
    if ($('#bot-health').length === 0) {
        const healthHtml = `
            <div class="mt-2">
                <small class="text-muted">Health: </small>
                <span id="bot-health" class="badge">Good</span>
                <span id="health-score" class="small text-muted"></span>
            </div>
        `;
        $('#bot-status').parent().append(healthHtml);
    }

    const healthBadge = $('#bot-health');
    const scoreSpan = $('#health-score');

    if (health.status === 'excellent') {
        healthBadge.removeClass('bg-warning bg-danger bg-secondary').addClass('bg-success').text('Excellent');
    } else if (health.status === 'good') {
        healthBadge.removeClass('bg-success bg-danger bg-secondary').addClass('bg-primary').text('Good');
    } else if (health.status === 'fair') {
        healthBadge.removeClass('bg-success bg-danger bg-primary').addClass('bg-warning').text('Fair');
    } else {
        healthBadge.removeClass('bg-success bg-warning bg-primary').addClass('bg-danger').text('Poor');
    }

    scoreSpan.text(`(${health.score}%)`);
}

function testTikTokAccess() {
    const button = $('#test-access');
    const originalText = button.html();

    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Testing...');

    $.post('/api/test-tiktok-access', {}, function(data) {
        if (data.success) {
            showAlert('TikTok access test completed successfully!', 'success');
            loadTikTokStatus(); // Refresh status
        } else {
            showAlert('TikTok access test failed: ' + data.message, 'danger');
        }
    }).fail(function() {
        showAlert('Failed to run TikTok access test', 'danger');
    }).always(function() {
        button.prop('disabled', false).html(originalText);
    });
}

function controlBot(action) {
    let button;
    if (action === 'start-manual-captcha') {
        button = $('#start-manual-captcha');
    } else if (action === 'start-bypass-captcha') {
        button = $('#start-bypass-captcha');
    } else {
        button = $('#' + action + '-bot');
    }

    const originalText = button.html();

    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Processing...');

    $.post('/api/bot/' + action, function(data) {
        if (data.success) {
            showAlert('success', data.message);
            updateBotStatus();
        } else {
            showAlert('danger', data.message);
        }
    }).fail(function() {
        showAlert('danger', 'Failed to ' + action + ' bot');
    }).always(function() {
        button.prop('disabled', false).html(originalText);
    });
}

function testTelegram() {
    showAlert('info', 'Testing Telegram connection...');
    // This would need to be implemented in the backend
}

function showAlert(type, message) {
    const alert = $('<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                   message +
                   '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                   '</div>');
    
    $('.container-fluid').prepend(alert);
    
    setTimeout(function() {
        alert.alert('close');
    }, 5000);
}
</script>
{% endblock %}
