# proxy_handler.py

import random
import requests
from config import PROXY_LIST_PATH

def is_proxy_valid(proxy: str) -> bool:
    """Check if proxy is valid and accessible"""
    try:
        # Parse proxy format (support both ip:port and user:pass@ip:port)
        if '@' in proxy:
            auth_part, server_part = proxy.split('@')
            proxy_url = f"http://{auth_part}@{server_part}"
        else:
            proxy_url = f"http://{proxy}"

        proxies = {
            "http": proxy_url,
            "https": proxy_url,
        }

        # Test with a simple HTTP request first
        test_urls = [
            "http://httpbin.org/ip",  # Simple IP check
            "https://www.google.com",  # Basic connectivity
            "https://www.tiktok.com"   # Target site
        ]

        for url in test_urls:
            try:
                response = requests.get(url, proxies=proxies, timeout=15)
                if response.status_code == 200:
                    # Additional check for TikTok - avoid blocked regions
                    if "tiktok.com" in url:
                        # Check if we're getting a proper TikTok page, not a block page
                        content = response.text.lower()
                        blocked_indicators = [
                            "access denied",
                            "blocked",
                            "not available in your region",
                            "service unavailable",
                            "403 forbidden"
                        ]
                        if any(indicator in content for indicator in blocked_indicators):
                            continue
                    return True
            except Exception:
                continue

        return False
    except Exception:
        return False

def load_proxies() -> list:
    """Load proxies from file with error handling"""
    try:
        with open(PROXY_LIST_PATH, "r", encoding='utf-8') as f:
            proxies = []
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # Skip empty lines and comments
                    # Basic validation of proxy format
                    if ':' in line:
                        proxies.append(line)
                    else:
                        print(f"[WARNING] Invalid proxy format at line {line_num}: {line}")
            return proxies
    except FileNotFoundError:
        print(f"[ERROR] Proxy file not found: {PROXY_LIST_PATH}")
        return []
    except Exception as e:
        print(f"[ERROR] Failed to load proxies: {e}")
        return []

def get_valid_proxy() -> str | None:
    """Get a valid proxy from the list"""
    proxies = load_proxies()
    if not proxies:
        print("[WARNING] No proxies available")
        return None

    # Shuffle to distribute load
    random.shuffle(proxies)

    print(f"[INFO] Testing {len(proxies)} proxies...")
    for i, proxy in enumerate(proxies):
        print(f"[INFO] Testing proxy {i+1}/{len(proxies)}: {proxy}")
        if is_proxy_valid(proxy):
            print(f"[SUCCESS] Valid proxy found: {proxy}")
            return proxy
        else:
            print(f"[FAILED] Proxy invalid: {proxy}")

    print("[WARNING] No valid proxies found")
    return None