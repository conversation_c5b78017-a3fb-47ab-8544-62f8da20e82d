# proxy_handler.py

import random
import requests
import json
import time
from typing import List, Dict, Optional
from config import PROXY_LIST_PATH

def is_proxy_valid(proxy: str) -> bool:
    """Check if proxy is valid and accessible"""
    try:
        # Parse proxy format (support both ip:port and user:pass@ip:port)
        if '@' in proxy:
            auth_part, server_part = proxy.split('@')
            proxy_url = f"http://{auth_part}@{server_part}"
        else:
            proxy_url = f"http://{proxy}"

        proxies = {
            "http": proxy_url,
            "https": proxy_url,
        }

        # Test with a simple HTTP request first
        test_urls = [
            "http://httpbin.org/ip",  # Simple IP check
            "https://www.google.com",  # Basic connectivity
            "https://www.tiktok.com"   # Target site
        ]

        for url in test_urls:
            try:
                response = requests.get(url, proxies=proxies, timeout=15)
                if response.status_code == 200:
                    # Additional check for TikTok - avoid blocked regions
                    if "tiktok.com" in url:
                        # Check if we're getting a proper TikTok page, not a block page
                        content = response.text.lower()
                        blocked_indicators = [
                            "access denied",
                            "blocked",
                            "not available in your region",
                            "service unavailable",
                            "403 forbidden"
                        ]
                        if any(indicator in content for indicator in blocked_indicators):
                            continue
                    return True
            except Exception:
                continue

        return False
    except Exception:
        return False

def load_proxies() -> list:
    """Load proxies from file with error handling"""
    try:
        with open(PROXY_LIST_PATH, "r", encoding='utf-8') as f:
            proxies = []
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # Skip empty lines and comments
                    # Basic validation of proxy format
                    if ':' in line:
                        proxies.append(line)
                    else:
                        print(f"[WARNING] Invalid proxy format at line {line_num}: {line}")
            return proxies
    except FileNotFoundError:
        print(f"[ERROR] Proxy file not found: {PROXY_LIST_PATH}")
        return []
    except Exception as e:
        print(f"[ERROR] Failed to load proxies: {e}")
        return []

def get_valid_proxy() -> str | None:
    """Get a valid proxy from the list"""
    proxies = load_proxies()
    if not proxies:
        print("[WARNING] No proxies available")
        return None

    # Shuffle to distribute load
    random.shuffle(proxies)

    print(f"[INFO] Testing {len(proxies)} proxies...")
    for i, proxy in enumerate(proxies):
        print(f"[INFO] Testing proxy {i+1}/{len(proxies)}: {proxy}")
        if is_proxy_valid(proxy):
            print(f"[SUCCESS] Valid proxy found: {proxy}")
            return proxy
        else:
            print(f"[FAILED] Proxy invalid: {proxy}")

    print("[WARNING] No valid proxies found")
    return None

# ==================== AUTO PROXY APIs ====================

def get_free_proxies_from_api() -> List[str]:
    """Get free proxies from various APIs"""
    proxies = []

    # API 1: ProxyScrape
    try:
        print("[INFO] Fetching proxies from ProxyScrape...")
        response = requests.get(
            "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all",
            timeout=15
        )
        if response.status_code == 200 and response.text.strip():
            proxy_list = [p.strip() for p in response.text.strip().split('\n') if p.strip() and ':' in p.strip()]
            proxies.extend(proxy_list)
            print(f"[SUCCESS] Got {len(proxy_list)} proxies from ProxyScrape")
    except Exception as e:
        print(f"[ERROR] ProxyScrape API failed: {e}")

    # API 2: Free Proxy List
    try:
        print("[INFO] Fetching proxies from Free Proxy List...")
        response = requests.get(
            "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
            timeout=15
        )
        if response.status_code == 200 and response.text.strip():
            proxy_list = [p.strip() for p in response.text.strip().split('\n') if p.strip() and ':' in p.strip()]
            proxies.extend(proxy_list)
            print(f"[SUCCESS] Got {len(proxy_list)} proxies from Free Proxy List")
    except Exception as e:
        print(f"[ERROR] Free Proxy List API failed: {e}")

    # API 3: ProxyList GitHub
    try:
        print("[INFO] Fetching proxies from ProxyList GitHub...")
        response = requests.get(
            "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt",
            timeout=15
        )
        if response.status_code == 200 and response.text.strip():
            proxy_list = [p.strip() for p in response.text.strip().split('\n') if p.strip() and ':' in p.strip()]
            proxies.extend(proxy_list)
            print(f"[SUCCESS] Got {len(proxy_list)} proxies from ProxyList GitHub")
    except Exception as e:
        print(f"[ERROR] ProxyList GitHub API failed: {e}")

    # API 4: Proxy11
    try:
        print("[INFO] Fetching proxies from Proxy11...")
        response = requests.get(
            "https://raw.githubusercontent.com/roosterkid/openproxylist/main/HTTPS_RAW.txt",
            timeout=15
        )
        if response.status_code == 200 and response.text.strip():
            proxy_list = [p.strip() for p in response.text.strip().split('\n') if p.strip() and ':' in p.strip()]
            proxies.extend(proxy_list)
            print(f"[SUCCESS] Got {len(proxy_list)} proxies from Proxy11")
    except Exception as e:
        print(f"[ERROR] Proxy11 API failed: {e}")

    # API 5: MomoProxy
    try:
        print("[INFO] Fetching proxies from MomoProxy...")
        response = requests.get(
            "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt",
            timeout=15
        )
        if response.status_code == 200 and response.text.strip():
            proxy_list = [p.strip() for p in response.text.strip().split('\n') if p.strip() and ':' in p.strip()]
            proxies.extend(proxy_list)
            print(f"[SUCCESS] Got {len(proxy_list)} proxies from MomoProxy")
    except Exception as e:
        print(f"[ERROR] MomoProxy API failed: {e}")

    # Filter out invalid formats and remove duplicates
    valid_proxies = []
    for proxy in proxies:
        if proxy and ':' in proxy and len(proxy.split(':')) == 2:
            try:
                ip, port = proxy.split(':')
                # Basic IP validation
                parts = ip.split('.')
                if len(parts) == 4 and all(0 <= int(part) <= 255 for part in parts):
                    # Basic port validation
                    if 1 <= int(port) <= 65535:
                        valid_proxies.append(proxy)
            except (ValueError, IndexError):
                continue

    unique_proxies = list(set(valid_proxies))
    print(f"[INFO] Total unique valid proxies fetched: {len(unique_proxies)}")
    return unique_proxies

def get_premium_proxies_from_api(api_key: str = None) -> List[str]:
    """Get premium proxies from paid APIs"""
    proxies = []

    if not api_key:
        print("[INFO] No premium API key provided, skipping premium proxies")
        return proxies

    # Premium API example - ProxyMesh
    try:
        print("[INFO] Fetching premium proxies...")
        # This is an example - replace with actual premium API
        headers = {"Authorization": f"Bearer {api_key}"}
        response = requests.get(
            "https://api.proxymesh.com/v1/proxy/list",
            headers=headers,
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            for proxy_info in data.get('proxies', []):
                proxy = f"{proxy_info['ip']}:{proxy_info['port']}"
                proxies.append(proxy)
            print(f"[SUCCESS] Got {len(proxies)} premium proxies")
    except Exception as e:
        print(f"[ERROR] Premium proxy API failed: {e}")

    return proxies

def update_proxy_file_with_api_proxies(api_key: str = None) -> bool:
    """Update proxy file with fresh proxies from APIs"""
    try:
        print("[INFO] Updating proxy file with fresh proxies...")

        # Get proxies from APIs
        free_proxies = get_free_proxies_from_api()
        premium_proxies = get_premium_proxies_from_api(api_key)

        all_proxies = free_proxies + premium_proxies

        if not all_proxies:
            print("[WARNING] No proxies fetched from APIs")
            return False

        # Test a sample of proxies to ensure quality
        print(f"[INFO] Testing sample of {min(20, len(all_proxies))} proxies...")
        random.shuffle(all_proxies)
        valid_proxies = []

        for i, proxy in enumerate(all_proxies[:20]):  # Test first 20
            print(f"[INFO] Testing proxy {i+1}/20: {proxy}")
            if is_proxy_valid(proxy):
                valid_proxies.append(proxy)
                print(f"[SUCCESS] Valid proxy: {proxy}")
            else:
                print(f"[FAILED] Invalid proxy: {proxy}")

        if not valid_proxies:
            print("[WARNING] No valid proxies found in sample")
            return False

        # Add all proxies to file (including untested ones)
        with open(PROXY_LIST_PATH, 'w', encoding='utf-8') as f:
            f.write("# Auto-updated proxy list\n")
            f.write(f"# Updated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Total proxies: {len(all_proxies)}\n")
            f.write(f"# Tested valid: {len(valid_proxies)}\n\n")

            # Write tested valid proxies first
            f.write("# Tested valid proxies:\n")
            for proxy in valid_proxies:
                f.write(f"{proxy}\n")

            f.write("\n# Additional untested proxies:\n")
            for proxy in all_proxies:
                if proxy not in valid_proxies:
                    f.write(f"{proxy}\n")

        print(f"[SUCCESS] Updated proxy file with {len(all_proxies)} proxies ({len(valid_proxies)} tested valid)")
        return True

    except Exception as e:
        print(f"[ERROR] Failed to update proxy file: {e}")
        return False

def get_auto_proxy(api_key: str = None) -> Optional[str]:
    """Get a working proxy automatically - try file first, then APIs"""

    # First try existing proxies from file
    print("[INFO] Trying existing proxies from file...")
    existing_proxy = get_valid_proxy()
    if existing_proxy:
        print(f"[SUCCESS] Found working proxy from file: {existing_proxy}")
        return existing_proxy

    # If no working proxy from file, fetch fresh ones from APIs
    print("[INFO] No working proxies in file, fetching fresh ones from APIs...")
    if update_proxy_file_with_api_proxies(api_key):
        # Try again with fresh proxies
        fresh_proxy = get_valid_proxy()
        if fresh_proxy:
            print(f"[SUCCESS] Found working proxy from fresh API fetch: {fresh_proxy}")
            return fresh_proxy

    print("[ERROR] Could not find any working proxy")
    return None