#!/usr/bin/env python3
# test_share_rules.py

"""
Test share rules
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def parse_share_rules(rules_str):
    """Parse share rules from string format"""
    rules = []
    try:
        for rule in rules_str.split(','):
            parts = rule.strip().split(':')
            if len(parts) == 3:
                min_likes = int(parts[0])
                max_likes = float('inf') if parts[1] == '999999999' else int(parts[1])
                shares = int(parts[2])
                rules.append((min_likes, max_likes, shares))
    except Exception as e:
        print(f"Error parsing rules: {e}")
    return rules

def calculate_share_count(likes):
    """Calculate share count based on likes"""
    # Use test rules that will share videos with low likes
    rules_str = "0:10:1,10:50:2,50:100:5,100:1000:50,1000:5000:100,5000:999999999:150"
    rules = parse_share_rules(rules_str)
    
    print(f"Rules: {rules}")
    print(f"Likes: {likes}")
    
    for min_likes, max_likes, shares in rules:
        if min_likes <= likes < max_likes:
            print(f"Match: {min_likes} <= {likes} < {max_likes} -> {shares} shares")
            return shares
    
    print("No match found")
    return 0

if __name__ == "__main__":
    print("🧪 Testing Share Rules")
    print("=" * 40)
    
    # Test different like counts
    test_likes = [2, 5, 13, 19, 25, 75, 150, 500, 1500, 6000]
    
    for likes in test_likes:
        shares = calculate_share_count(likes)
        print(f"📊 {likes} likes -> {shares} shares")
        print()
