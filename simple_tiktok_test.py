#!/usr/bin/env python3
# simple_tiktok_test.py

"""
Simple TikTok access test
"""

import requests
from proxy_handler import get_auto_proxy

def test_tiktok_simple():
    """Simple TikTok test"""
    print("🚀 Simple TikTok Access Test")
    print("=" * 40)
    
    # Get proxy
    proxy = get_auto_proxy()
    print(f"🌐 Using proxy: {proxy}")
    
    # Test without proxy first
    print("\n🔍 Test 1: Without proxy")
    try:
        response = requests.get('https://www.tiktok.com/', timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Content length: {len(response.text)}")
        if "tiktok" in response.text.lower():
            print("✅ TikTok accessible without proxy")
        else:
            print("❌ TikTok blocked without proxy")
    except Exception as e:
        print(f"❌ Error without proxy: {e}")
    
    # Test with proxy
    if proxy:
        print(f"\n🔍 Test 2: With proxy {proxy}")
        try:
            proxies = {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'
            }
            response = requests.get('https://www.tiktok.com/', proxies=proxies, timeout=15)
            print(f"Status: {response.status_code}")
            print(f"Content length: {len(response.text)}")
            if "tiktok" in response.text.lower():
                print("✅ TikTok accessible with proxy")
            else:
                print("❌ TikTok blocked with proxy")
        except Exception as e:
            print(f"❌ Error with proxy: {e}")
    
    # Test target user
    print(f"\n🔍 Test 3: Target user @hiamnoone")
    try:
        if proxy:
            proxies = {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'
            }
            response = requests.get('https://www.tiktok.com/@hiamnoone', proxies=proxies, timeout=15)
        else:
            response = requests.get('https://www.tiktok.com/@hiamnoone', timeout=10)
        
        print(f"Status: {response.status_code}")
        print(f"URL: {response.url}")
        if response.status_code == 200:
            print("✅ User page accessible")
        else:
            print("❌ User page not accessible")
    except Exception as e:
        print(f"❌ Error accessing user: {e}")

if __name__ == "__main__":
    test_tiktok_simple()
