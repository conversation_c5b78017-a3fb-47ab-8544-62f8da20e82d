#!/usr/bin/env python3
# find_share_button.py

"""
Find TikTok share button selector
"""

import asyncio
from playwright.async_api import async_playwright

async def find_share_button():
    """Find share button on TikTok video page"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # Show browser
        context = await browser.new_context()
        page = await context.new_page()
        
        # Go to a TikTok video
        video_url = "https://www.tiktok.com/@hiamnoone/video/7524160831790140690"
        print(f"🎥 Loading video: {video_url}")
        
        await page.goto(video_url, wait_until='load')
        await page.wait_for_timeout(5000)  # Wait for page to load
        
        # Try different share button selectors
        selectors = [
            "button[data-e2e='share-button']",
            "button[aria-label*='Share']",
            "button[title*='Share']",
            "[data-e2e*='share']",
            "button:has-text('Share')",
            "svg[fill*='currentColor'] + span:has-text('Share')",
            "div[data-e2e='video-share-container']",
            "div[data-e2e='video-tool'] button",
            "[data-e2e='video-tool-item-share']",
            "button[data-e2e='video-tool-item-share']"
        ]
        
        print("\n🔍 Testing share button selectors:")
        print("-" * 50)
        
        for i, selector in enumerate(selectors):
            try:
                elements = await page.query_selector_all(selector)
                if elements:
                    print(f"✅ {i+1}. {selector} -> Found {len(elements)} element(s)")
                    
                    # Get text content of first element
                    try:
                        text = await elements[0].inner_text()
                        print(f"   Text: '{text}'")
                    except:
                        print(f"   (No text content)")
                        
                    # Get attributes
                    try:
                        attrs = await elements[0].evaluate("el => Array.from(el.attributes).map(attr => `${attr.name}='${attr.value}'`).join(' ')")
                        print(f"   Attributes: {attrs}")
                    except:
                        pass
                        
                else:
                    print(f"❌ {i+1}. {selector} -> Not found")
            except Exception as e:
                print(f"❌ {i+1}. {selector} -> Error: {e}")
        
        print("\n🔍 Looking for all buttons on page:")
        print("-" * 50)
        
        try:
            buttons = await page.query_selector_all("button")
            print(f"Found {len(buttons)} buttons total")
            
            for i, button in enumerate(buttons[:20]):  # Show first 20 buttons
                try:
                    text = await button.inner_text()
                    attrs = await button.evaluate("el => Array.from(el.attributes).map(attr => `${attr.name}='${attr.value}'`).join(' ')")
                    if text.strip() or 'share' in attrs.lower():
                        print(f"Button {i+1}: '{text}' | {attrs}")
                except:
                    pass
                    
        except Exception as e:
            print(f"Error getting buttons: {e}")
        
        print("\n⏸️ Browser will stay open for 30 seconds for manual inspection...")
        await page.wait_for_timeout(30000)
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(find_share_button())
