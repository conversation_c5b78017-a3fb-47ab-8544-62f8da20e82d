import json
import os
import asyncio
from datetime import datetime
from config import SHARE_RULES, TARGET_USERNAMES

def get_target_usernames():
    return TARGET_USERNAMES

def calculate_share_count(like_count):
    """Calculate share count based on like count using SHARE_RULES from config"""
    for min_likes, max_likes, shares in SHARE_RULES:
        if min_likes <= like_count < max_likes:
            return shares
    return 0

def parse_like_count(like_text):
    """Parse like count from TikTok text format (e.g., '1.2K', '3.5M')"""
    if not like_text:
        return 0

    like_text = like_text.strip().upper()

    # Remove any non-numeric characters except K, M, and decimal point
    import re
    clean_text = re.sub(r'[^\d.KM]', '', like_text)

    try:
        if 'M' in clean_text:
            # Handle millions (e.g., '1.2M' -> 1200000)
            number = float(clean_text.replace('M', ''))
            return int(number * 1000000)
        elif 'K' in clean_text:
            # Handle thousands (e.g., '1.2K' -> 1200)
            number = float(clean_text.replace('K', ''))
            return int(number * 1000)
        else:
            # Handle regular numbers
            return int(float(clean_text))
    except (ValueError, TypeError):
        return 0

def load_previous_data(file_path="video_data.json"):
    """Load previously shared video data"""
    if os.path.exists(file_path):
        try:
            with open(file_path, "r", encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            return {}
    return {}

def save_video_data(data, file_path="video_data.json"):
    """Save video data to JSON file"""
    try:
        with open(file_path, "w", encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
    except IOError as e:
        log_to_console(f"[ERROR] Failed to save video data: {e}")

def already_shared_videos(video_url):
    """Check if video has already been shared"""
    data = load_previous_data()
    return video_url in data

def mark_video_shared(video_url, share_count):
    """Mark video as shared with timestamp and share count"""
    data = load_previous_data()
    data[video_url] = {
        "shared_count": share_count,
        "timestamp": datetime.now().isoformat(),
        "last_shared": datetime.now().isoformat()
    }
    save_video_data(data)

def log_to_console(message):
    """Log message to console with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_message = f"[{timestamp}] {message}"
    print(formatted_message)

    # Also save to log file
    try:
        with open("log.txt", "a", encoding='utf-8') as f:
            f.write(formatted_message + "\n")
    except IOError:
        pass  # Ignore file write errors

async def wait_with_retries(page, selector, timeout=10000, retries=3):
    """Wait for selector with retries"""
    for attempt in range(retries):
        try:
            await page.wait_for_selector(selector, timeout=timeout)
            return True
        except Exception as e:
            if attempt == retries - 1:
                log_to_console(f"[ERROR] Failed to find selector '{selector}' after {retries} attempts: {e}")
                return False
            await asyncio.sleep(1)
    return False

def update_video_share_count(video_id, share_count, video_data):
    """Update video share count in data structure"""
    if video_id not in video_data:
        video_data[video_id] = {"shared": 0}
    video_data[video_id]["shared"] += share_count
    return video_data

def get_share_count_based_on_likes(like_count):
    """Legacy function - use calculate_share_count instead"""
    return calculate_share_count(like_count)