#!/usr/bin/env python3
# tiktok_account_manager.py

"""
TikTok Account Creation and Management
"""

import asyncio
import random
import string
import json
from datetime import datetime
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
from telegram_logger import send_telegram_message
from utils import log_to_console

class TikTokAccountManager:
    def __init__(self):
        self.accounts_file = "tiktok_accounts.json"
        self.accounts = self.load_accounts()
    
    def load_accounts(self):
        """Load existing accounts from file"""
        try:
            with open(self.accounts_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return []
    
    def save_accounts(self):
        """Save accounts to file"""
        with open(self.accounts_file, 'w') as f:
            json.dump(self.accounts, f, indent=2)
    
    def generate_account_data(self):
        """Generate random account data"""
        # Generate random username
        username = f"user{''.join(random.choices(string.ascii_lowercase + string.digits, k=8))}"
        
        # Generate random email
        email_providers = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com']
        email = f"{username}@{random.choice(email_providers)}"
        
        # Generate random password
        password = ''.join(random.choices(string.ascii_letters + string.digits + '!@#$%', k=12))
        
        # Generate random profile data
        first_names = ['Alex', 'Jordan', 'Taylor', 'Casey', 'Morgan', '<PERSON>', '<PERSON>', 'Quinn']
        last_names = ['Smith', 'Johnson', 'Brown', '<PERSON>', 'Miller', 'Wilson', 'Moore', 'Taylor']
        
        return {
            'username': username,
            'email': email,
            'password': password,
            'first_name': random.choice(first_names),
            'last_name': random.choice(last_names),
            'created_at': datetime.now().isoformat(),
            'status': 'pending'
        }
    
    async def create_account_playwright(self, account_data, proxy=None):
        """Create TikTok account using Playwright"""
        try:
            log_to_console(f"Creating TikTok account: {account_data['username']}")
            await send_telegram_message(f"🔄 Creating TikTok account: {account_data['username']}")
            
            async with async_playwright() as p:
                # Browser setup
                browser_args = [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-automation',
                    '--disable-extensions',
                    '--no-first-run',
                    '--disable-default-apps',
                    '--disable-popup-blocking',
                    '--disable-translate',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-ipc-flooding-protection'
                ]
                
                if proxy:
                    browser_args.append(f'--proxy-server=http://{proxy}')
                
                browser = await p.chromium.launch(
                    headless=False,  # Show browser for CAPTCHA solving
                    args=browser_args
                )
                
                context = await browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                )
                
                page = await context.new_page()
                
                # Navigate to TikTok signup
                await page.goto('https://www.tiktok.com/signup', wait_until='load')
                await asyncio.sleep(3)
                
                # Try different signup methods
                signup_selectors = [
                    'a[href*="signup"]',
                    'button:has-text("Sign up")',
                    '[data-e2e="sign-up"]'
                ]
                
                signup_clicked = False
                for selector in signup_selectors:
                    try:
                        signup_btn = await page.query_selector(selector)
                        if signup_btn:
                            await signup_btn.click()
                            signup_clicked = True
                            break
                    except:
                        continue
                
                if not signup_clicked:
                    log_to_console("Could not find signup button")
                    await browser.close()
                    return False
                
                await asyncio.sleep(3)
                
                # Choose email signup method
                email_signup_selectors = [
                    'div:has-text("Use email")',
                    'button:has-text("Use email")',
                    '[data-e2e="email-signup"]'
                ]
                
                for selector in email_signup_selectors:
                    try:
                        email_btn = await page.query_selector(selector)
                        if email_btn:
                            await email_btn.click()
                            break
                    except:
                        continue
                
                await asyncio.sleep(2)
                
                # Fill email
                email_selectors = [
                    'input[type="email"]',
                    'input[name="email"]',
                    'input[placeholder*="email"]'
                ]
                
                for selector in email_selectors:
                    try:
                        email_field = await page.query_selector(selector)
                        if email_field:
                            await email_field.fill(account_data['email'])
                            break
                    except:
                        continue
                
                # Fill password
                password_selectors = [
                    'input[type="password"]',
                    'input[name="password"]',
                    'input[placeholder*="password"]'
                ]
                
                for selector in password_selectors:
                    try:
                        password_field = await page.query_selector(selector)
                        if password_field:
                            await password_field.fill(account_data['password'])
                            break
                    except:
                        continue
                
                # Fill username if available
                username_selectors = [
                    'input[name="username"]',
                    'input[placeholder*="username"]'
                ]
                
                for selector in username_selectors:
                    try:
                        username_field = await page.query_selector(selector)
                        if username_field:
                            await username_field.fill(account_data['username'])
                            break
                    except:
                        continue
                
                log_to_console("Form filled. Please solve CAPTCHA manually if needed...")
                await send_telegram_message("📝 Form filled. Solve CAPTCHA manually if needed.")
                
                # Wait for manual CAPTCHA solving
                print("\n" + "="*60)
                print("🤖 MANUAL INTERVENTION NEEDED")
                print("="*60)
                print("1. Solve CAPTCHA if it appears")
                print("2. Complete any additional verification")
                print("3. Click 'Sign up' or 'Create account'")
                print("4. Press Enter here when account is created...")
                print("="*60)
                
                input("Press Enter when account creation is complete...")
                
                # Check if account was created successfully
                current_url = page.url
                if 'tiktok.com' in current_url and 'signup' not in current_url:
                    log_to_console(f"✅ Account created successfully: {account_data['username']}")
                    await send_telegram_message(f"✅ Account created: {account_data['username']}")
                    
                    account_data['status'] = 'created'
                    account_data['profile_url'] = current_url
                    
                    await browser.close()
                    return True
                else:
                    log_to_console(f"❌ Account creation failed: {account_data['username']}")
                    await send_telegram_message(f"❌ Account creation failed: {account_data['username']}")
                    
                    account_data['status'] = 'failed'
                    
                    await browser.close()
                    return False
                
        except Exception as e:
            log_to_console(f"❌ Error creating account: {e}")
            await send_telegram_message(f"❌ Error creating account: {str(e)[:100]}")
            account_data['status'] = 'error'
            account_data['error'] = str(e)
            return False
    
    async def create_multiple_accounts(self, count=1, proxy=None):
        """Create multiple TikTok accounts"""
        log_to_console(f"🚀 Creating {count} TikTok accounts...")
        await send_telegram_message(f"🚀 Starting creation of {count} TikTok accounts")
        
        created_accounts = []
        
        for i in range(count):
            log_to_console(f"📝 Creating account {i+1}/{count}")
            
            # Generate account data
            account_data = self.generate_account_data()
            
            # Create account
            success = await self.create_account_playwright(account_data, proxy)
            
            # Save account data
            self.accounts.append(account_data)
            self.save_accounts()
            
            if success:
                created_accounts.append(account_data)
            
            # Wait between account creations
            if i < count - 1:
                wait_time = random.randint(300, 600)  # 5-10 minutes
                log_to_console(f"⏳ Waiting {wait_time//60} minutes before next account...")
                await asyncio.sleep(wait_time)
        
        log_to_console(f"✅ Account creation completed. {len(created_accounts)}/{count} successful")
        await send_telegram_message(f"✅ Created {len(created_accounts)}/{count} accounts successfully")
        
        return created_accounts
    
    def get_accounts(self):
        """Get all accounts"""
        return self.accounts
    
    def get_account_by_username(self, username):
        """Get account by username"""
        for account in self.accounts:
            if account['username'] == username:
                return account
        return None
    
    def update_account_status(self, username, status, notes=None):
        """Update account status"""
        for account in self.accounts:
            if account['username'] == username:
                account['status'] = status
                account['updated_at'] = datetime.now().isoformat()
                if notes:
                    account['notes'] = notes
                break
        self.save_accounts()

# Example usage
async def main():
    """Example usage"""
    manager = TikTokAccountManager()
    
    # Create 1 account
    await manager.create_multiple_accounts(count=1)
    
    # List all accounts
    accounts = manager.get_accounts()
    print(f"Total accounts: {len(accounts)}")
    for account in accounts:
        print(f"- {account['username']} ({account['status']})")

if __name__ == "__main__":
    asyncio.run(main())
