{% extends "base.html" %}

{% block title %}Settings - TikTok Bot Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-cog me-2"></i>Bot Settings</h2>
        <p class="text-muted">Configure your TikTok bot settings below</p>
    </div>
</div>

<div class="row">
    <!-- Target Users Settings -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-bullseye me-2"></i>Target Users</h5>
            </div>
            <div class="card-body">
                <form id="target-users-form">
                    <div class="mb-3">
                        <label for="target-usernames" class="form-label">TikTok Usernames</label>
                        <textarea class="form-control" id="target-usernames" rows="5" 
                                  placeholder="Enter usernames, one per line or comma-separated">{{ settings.target_usernames|join('\n') }}</textarea>
                        <div class="form-text">Enter TikTok usernames without @ symbol</div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Target Users
                        </button>
                    </div>
                </form>
                
                <!-- Current Target Users -->
                <hr>
                <h6>Current Target Users:</h6>
                <div id="current-users">
                    {% if settings.target_usernames %}
                        {% for user in settings.target_usernames %}
                        <span class="badge bg-secondary me-1 mb-1">
                            @{{ user }}
                            <button type="button" class="btn-close btn-close-white ms-1" 
                                    onclick="removeUser('{{ user }}')" style="font-size: 0.6em;"></button>
                        </span>
                        {% endfor %}
                    {% else %}
                        <span class="text-muted">No users configured</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- TikTok Account Settings -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fab fa-tiktok me-2"></i>TikTok Account</h5>
            </div>
            <div class="card-body">
                <form id="tiktok-account-form">
                    <div class="mb-3">
                        <label for="tiktok-username" class="form-label">TikTok Username</label>
                        <input type="text" class="form-control" id="tiktok-username" 
                               value="{{ settings.tiktok_username }}" placeholder="Your TikTok username">
                    </div>
                    
                    <div class="mb-3">
                        <label for="tiktok-password" class="form-label">TikTok Password</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="tiktok-password" 
                                   value="{{ settings.tiktok_password }}" placeholder="Your TikTok password">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                <i class="fas fa-eye" id="password-toggle-icon"></i>
                            </button>
                        </div>
                        <div class="form-text">Used for login if required by TikTok</div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-save me-2"></i>Save TikTok Account
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- Telegram Settings -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fab fa-telegram me-2"></i>Telegram Settings</h5>
            </div>
            <div class="card-body">
                <form id="telegram-form">
                    <div class="mb-3">
                        <label for="telegram-token" class="form-label">Bot Token</label>
                        <input type="text" class="form-control" id="telegram-token" 
                               value="{{ settings.telegram_token }}" placeholder="Bot token from @BotFather">
                    </div>
                    
                    <div class="mb-3">
                        <label for="telegram-user-id" class="form-label">User ID</label>
                        <input type="number" class="form-control" id="telegram-user-id" 
                               value="{{ settings.telegram_user_id }}" placeholder="Your Telegram user ID">
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>Save Telegram Settings
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="testTelegram()">
                            <i class="fas fa-paper-plane me-2"></i>Test Connection
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Share Rules Settings -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-share me-2"></i>Share Rules</h5>
            </div>
            <div class="card-body">
                <form id="share-rules-form">
                    <div class="mb-3">
                        <label class="form-label">Current Share Rules</label>
                        <div id="share-rules-list">
                            {% for rule in settings.share_rules %}
                            <div class="input-group mb-2">
                                <span class="input-group-text">{{ rule[0] }}</span>
                                <span class="input-group-text">-</span>
                                <span class="input-group-text">{{ '∞' if rule[1] >= 999999999 else rule[1] }}</span>
                                <span class="input-group-text">likes →</span>
                                <input type="number" class="form-control" value="{{ rule[2] }}" min="0">
                                <span class="input-group-text">shares</span>
                                <button class="btn btn-outline-danger" type="button" onclick="removeRule(this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-warning" onclick="addRule()">
                            <i class="fas fa-plus me-2"></i>Add Rule
                        </button>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-2"></i>Save Share Rules
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- Bot Settings -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="fas fa-robot me-2"></i>Bot Settings</h5>
            </div>
            <div class="card-body">
                <form id="bot-settings-form">
                    <div class="mb-3">
                        <label for="recheck-interval" class="form-label">Recheck Interval (hours)</label>
                        <input type="number" class="form-control" id="recheck-interval" 
                               value="{{ settings.recheck_interval }}" min="1" max="24">
                        <div class="form-text">How often to check for new videos</div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-secondary">
                            <i class="fas fa-save me-2"></i>Save Bot Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Actions -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="exportSettings()">
                        <i class="fas fa-download me-2"></i>Export Settings
                    </button>
                    <button class="btn btn-outline-info" onclick="importSettings()">
                        <i class="fas fa-upload me-2"></i>Import Settings
                    </button>
                    <button class="btn btn-outline-warning" onclick="resetSettings()">
                        <i class="fas fa-undo me-2"></i>Reset to Defaults
                    </button>
                    <button class="btn btn-outline-danger" onclick="clearData()">
                        <i class="fas fa-trash me-2"></i>Clear All Data
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function togglePassword() {
    const passwordField = document.getElementById('tiktok-password');
    const toggleIcon = document.getElementById('password-toggle-icon');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordField.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

function removeUser(username) {
    // This would update the textarea and current users display
    const textarea = document.getElementById('target-usernames');
    const lines = textarea.value.split('\n');
    const filtered = lines.filter(line => line.trim() !== username);
    textarea.value = filtered.join('\n');
    
    // Update display
    location.reload(); // Simple reload for now
}

function addRule() {
    const container = document.getElementById('share-rules-list');
    const newRule = document.createElement('div');
    newRule.className = 'input-group mb-2';
    newRule.innerHTML = `
        <input type="number" class="form-control" placeholder="Min likes" min="0">
        <span class="input-group-text">-</span>
        <input type="number" class="form-control" placeholder="Max likes" min="0">
        <span class="input-group-text">likes →</span>
        <input type="number" class="form-control" placeholder="Shares" min="0">
        <span class="input-group-text">shares</span>
        <button class="btn btn-outline-danger" type="button" onclick="removeRule(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(newRule);
}

function removeRule(button) {
    button.closest('.input-group').remove();
}

function testTelegram() {
    showAlert('info', 'Testing Telegram connection...');
    // Implementation would go here
}

function exportSettings() {
    showAlert('info', 'Exporting settings...');
    // Implementation would go here
}

function importSettings() {
    showAlert('info', 'Import settings functionality coming soon...');
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
        showAlert('warning', 'Reset functionality coming soon...');
    }
}

function clearData() {
    if (confirm('Are you sure you want to clear all data? This cannot be undone!')) {
        showAlert('danger', 'Clear data functionality coming soon...');
    }
}

function showAlert(type, message) {
    const alert = $('<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                   message +
                   '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                   '</div>');
    
    $('.container-fluid').prepend(alert);
    
    setTimeout(function() {
        alert.alert('close');
    }, 5000);
}

// Form submissions
$(document).ready(function() {
    $('#target-users-form').submit(function(e) {
        e.preventDefault();
        saveTargetUsers();
    });

    $('#tiktok-account-form').submit(function(e) {
        e.preventDefault();
        saveTikTokAccount();
    });

    $('#telegram-form').submit(function(e) {
        e.preventDefault();
        saveTelegramSettings();
    });

    $('#share-rules-form').submit(function(e) {
        e.preventDefault();
        saveShareRules();
    });

    $('#bot-settings-form').submit(function(e) {
        e.preventDefault();
        saveBotSettings();
    });
});

function saveTargetUsers() {
    const textarea = $('#target-usernames');
    const button = $('#target-users-form button[type="submit"]');
    const originalText = button.html();

    // Parse usernames from textarea
    const usernamesText = textarea.val().trim();
    let usernames = [];

    if (usernamesText) {
        // Split by newlines or commas, clean up
        usernames = usernamesText
            .split(/[\n,]/)
            .map(u => u.trim().replace('@', ''))
            .filter(u => u.length > 0);
    }

    // Update button state
    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Saving...');

    // Send to backend
    $.ajax({
        url: '/api/settings/target-users',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            target_usernames: usernames
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                updateCurrentUsersDisplay(usernames);
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Failed to save target users');
        },
        complete: function() {
            button.prop('disabled', false).html(originalText);
        }
    });
}

function saveTikTokAccount() {
    const username = $('#tiktok-username').val().trim();
    const password = $('#tiktok-password').val().trim();
    const button = $('#tiktok-account-form button[type="submit"]');
    const originalText = button.html();

    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Saving...');

    $.ajax({
        url: '/api/settings/tiktok',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            tiktok_username: username,
            tiktok_password: password
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Failed to save TikTok account');
        },
        complete: function() {
            button.prop('disabled', false).html(originalText);
        }
    });
}

function saveTelegramSettings() {
    const token = $('#telegram-token').val().trim();
    const userId = $('#telegram-user-id').val().trim();
    const button = $('#telegram-form button[type="submit"]');
    const originalText = button.html();

    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Saving...');

    $.ajax({
        url: '/api/settings/telegram',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            telegram_token: token,
            telegram_user_id: parseInt(userId) || 0
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Failed to save Telegram settings');
        },
        complete: function() {
            button.prop('disabled', false).html(originalText);
        }
    });
}

function saveShareRules() {
    showAlert('info', 'Share rules saving functionality coming soon...');
}

function saveBotSettings() {
    const interval = $('#recheck-interval').val();
    const button = $('#bot-settings-form button[type="submit"]');
    const originalText = button.html();

    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Saving...');

    $.ajax({
        url: '/api/settings/bot',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            recheck_interval: parseInt(interval) || 2
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Failed to save bot settings');
        },
        complete: function() {
            button.prop('disabled', false).html(originalText);
        }
    });
}

function updateCurrentUsersDisplay(usernames) {
    const container = $('#current-users');
    container.empty();

    if (usernames.length === 0) {
        container.html('<span class="text-muted">No users configured</span>');
        return;
    }

    usernames.forEach(function(user) {
        container.append(`
            <span class="badge bg-secondary me-1 mb-1">
                @${user}
                <button type="button" class="btn-close btn-close-white ms-1"
                        onclick="removeUser('${user}')" style="font-size: 0.6em;"></button>
            </span>
        `);
    });
}

function testTelegram() {
    const button = $('button[onclick="testTelegram()"]');
    const originalText = button.html();

    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Testing...');

    $.ajax({
        url: '/api/test-telegram',
        method: 'POST',
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Failed to test Telegram connection');
        },
        complete: function() {
            button.prop('disabled', false).html(originalText);
        }
    });
}
</script>
{% endblock %}
