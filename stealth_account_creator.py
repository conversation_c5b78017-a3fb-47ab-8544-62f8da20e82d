#!/usr/bin/env python3
# stealth_account_creator.py

"""
Stealth TikTok account creator to bypass access denied
"""

import asyncio
import random
import time
from playwright.async_api import async_playwright
from tiktok_account_manager import Tik<PERSON>okAccountManager
from telegram_logger import send_telegram_message
from utils import log_to_console

class StealthAccountCreator:
    def __init__(self):
        self.account_manager = TikTokAccountManager()
    
    async def create_stealth_account(self, proxy=None):
        """Create account with maximum stealth"""
        try:
            log_to_console("🥷 Starting stealth account creation...")
            await send_telegram_message("🥷 Starting stealth account creation")
            
            # Generate account data
            account_data = self.account_manager.generate_account_data()
            log_to_console(f"📝 Generated account: {account_data['username']}")
            
            async with async_playwright() as p:
                # Ultra stealth browser setup
                browser_args = [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--exclude-switches=enable-automation',
                    '--disable-extensions',
                    '--no-first-run',
                    '--disable-default-apps',
                    '--disable-popup-blocking',
                    '--disable-translate',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-ipc-flooding-protection',
                    '--disable-hang-monitor',
                    '--disable-features=TranslateUI',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-component-extensions-with-background-pages',
                    '--no-default-browser-check',
                    '--autoplay-policy=user-gesture-required',
                    '--disable-background-networking',
                    '--disable-sync',
                    '--metrics-recording-only',
                    '--disable-default-apps',
                    '--mute-audio',
                    '--no-report-upload',
                    '--disable-logging',
                    '--disable-permissions-api'
                ]
                
                if proxy:
                    browser_args.append(f'--proxy-server=http://{proxy}')
                
                # Random user agent
                user_agents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
                ]
                
                selected_ua = random.choice(user_agents)
                browser_args.append(f'--user-agent={selected_ua}')
                
                browser = await p.chromium.launch(
                    headless=False,
                    args=browser_args
                )
                
                # Create context with stealth settings
                context = await browser.new_context(
                    viewport={'width': random.randint(1200, 1920), 'height': random.randint(800, 1080)},
                    user_agent=selected_ua,
                    locale='en-US',
                    timezone_id='America/New_York',
                    permissions=['geolocation'],
                    geolocation={'latitude': 40.7128 + random.uniform(-0.1, 0.1), 'longitude': -74.0060 + random.uniform(-0.1, 0.1)},
                    extra_http_headers={
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'none',
                        'Cache-Control': 'max-age=0'
                    }
                )
                
                page = await context.new_page()
                
                # Add stealth scripts
                await page.add_init_script("""
                    // Remove webdriver property
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                    
                    // Mock plugins
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5],
                    });
                    
                    // Mock languages
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['en-US', 'en'],
                    });
                    
                    // Mock chrome object
                    window.chrome = {
                        runtime: {},
                        loadTimes: function() {},
                        csi: function() {},
                        app: {}
                    };
                    
                    // Mock permissions
                    const originalQuery = window.navigator.permissions.query;
                    window.navigator.permissions.query = (parameters) => (
                        parameters.name === 'notifications' ?
                            Promise.resolve({ state: Notification.permission }) :
                            originalQuery(parameters)
                    );
                    
                    // Override the `plugins` property to use a custom getter.
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5],
                    });
                """)
                
                # Human-like navigation
                log_to_console("🌐 Navigating to TikTok...")
                
                # First visit TikTok homepage to establish session
                await page.goto('https://www.tiktok.com/', wait_until='networkidle')
                await self.human_delay(3, 7)
                
                # Check for access denied
                page_content = await page.content()
                if 'access denied' in page_content.lower() or 'blocked' in page_content.lower():
                    log_to_console("❌ Access denied on homepage")
                    await send_telegram_message("❌ Access denied - trying alternative approach")
                    
                    # Try different approach
                    await page.goto('https://www.tiktok.com/explore', wait_until='networkidle')
                    await self.human_delay(2, 4)
                
                # Navigate to signup
                log_to_console("📝 Going to signup page...")
                await page.goto('https://www.tiktok.com/signup', wait_until='networkidle')
                await self.human_delay(3, 6)
                
                # Check for access denied again
                current_url = page.url
                page_title = await page.title()
                
                if 'access denied' in page_title.lower() or 'blocked' in current_url.lower():
                    log_to_console("❌ Still getting access denied")
                    await send_telegram_message("❌ Access denied persists - may need different proxy/IP")
                    await browser.close()
                    return False
                
                log_to_console("✅ Successfully accessed TikTok signup page")
                await send_telegram_message("✅ Accessed signup page successfully")
                
                # Continue with account creation
                success = await self.complete_signup(page, account_data)
                
                await browser.close()
                
                if success:
                    account_data['status'] = 'created'
                    self.account_manager.accounts.append(account_data)
                    self.account_manager.save_accounts()
                    log_to_console(f"✅ Account created successfully: {account_data['username']}")
                    await send_telegram_message(f"✅ Account created: {account_data['username']}")
                    return True
                else:
                    account_data['status'] = 'failed'
                    self.account_manager.accounts.append(account_data)
                    self.account_manager.save_accounts()
                    return False
                
        except Exception as e:
            log_to_console(f"❌ Stealth account creation failed: {e}")
            await send_telegram_message(f"❌ Stealth creation failed: {str(e)[:100]}")
            return False
    
    async def human_delay(self, min_seconds=1, max_seconds=3):
        """Human-like random delay"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def complete_signup(self, page, account_data):
        """Complete the signup process"""
        try:
            log_to_console("📝 Filling signup form...")
            
            # Look for email signup option
            await self.human_delay(2, 4)
            
            # Try multiple selectors for email signup
            email_signup_selectors = [
                'div:has-text("Use email")',
                'button:has-text("Use email")',
                'a:has-text("Use email")',
                '[data-e2e="email-signup"]',
                'div:has-text("Continue with email")',
                'button:has-text("Continue with email")'
            ]
            
            email_clicked = False
            for selector in email_signup_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        await element.click()
                        email_clicked = True
                        log_to_console("✅ Clicked email signup option")
                        break
                except:
                    continue
            
            if not email_clicked:
                log_to_console("⚠️ Could not find email signup option")
            
            await self.human_delay(2, 4)
            
            # Fill email
            email_selectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="Email"]'
            ]
            
            for selector in email_selectors:
                try:
                    email_field = await page.query_selector(selector)
                    if email_field:
                        await email_field.click()
                        await self.human_delay(0.5, 1)
                        await email_field.fill(account_data['email'])
                        log_to_console(f"✅ Filled email: {account_data['email']}")
                        break
                except:
                    continue
            
            await self.human_delay(1, 2)
            
            # Fill password
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[placeholder*="password"]',
                'input[placeholder*="Password"]'
            ]
            
            for selector in password_selectors:
                try:
                    password_field = await page.query_selector(selector)
                    if password_field:
                        await password_field.click()
                        await self.human_delay(0.5, 1)
                        await password_field.fill(account_data['password'])
                        log_to_console("✅ Filled password")
                        break
                except:
                    continue
            
            await self.human_delay(1, 2)
            
            log_to_console("📝 Form filled. Manual intervention needed for CAPTCHA...")
            await send_telegram_message("📝 Form filled. Please solve CAPTCHA manually.")
            
            print("\n" + "="*60)
            print("🤖 MANUAL INTERVENTION NEEDED")
            print("="*60)
            print(f"Account: {account_data['username']}")
            print(f"Email: {account_data['email']}")
            print(f"Password: {account_data['password']}")
            print("1. Solve CAPTCHA if it appears")
            print("2. Complete any additional verification")
            print("3. Click 'Sign up' or 'Create account'")
            print("4. Press Enter here when account is created...")
            print("="*60)
            
            input("Press Enter when account creation is complete...")
            
            # Check if account was created
            await self.human_delay(2, 4)
            current_url = page.url
            
            if 'tiktok.com' in current_url and 'signup' not in current_url:
                log_to_console("✅ Account creation appears successful")
                return True
            else:
                log_to_console("❌ Account creation may have failed")
                return False
                
        except Exception as e:
            log_to_console(f"❌ Error completing signup: {e}")
            return False

# Example usage
async def main():
    """Create stealth account"""
    creator = StealthAccountCreator()
    
    print("🥷 Stealth TikTok Account Creator")
    print("=" * 50)
    print("This will attempt to bypass access denied errors")
    print("=" * 50)
    
    # Ask for proxy
    proxy = input("Enter proxy (ip:port) or press Enter to skip: ").strip()
    if not proxy:
        proxy = None
    
    success = await creator.create_stealth_account(proxy)
    
    if success:
        print("🎉 Stealth account creation completed!")
    else:
        print("❌ Stealth account creation failed")

if __name__ == "__main__":
    asyncio.run(main())
